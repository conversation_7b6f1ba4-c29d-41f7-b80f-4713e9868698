/**
 * 预测性维护节点集合
 * 提供状态监控、故障预测、维护调度、零件更换、维护历史等预测性维护功能的节点
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';

/**
 * 设备健康状态枚举
 */
export enum HealthStatus {
  HEALTHY = 'healthy',
  WARNING = 'warning',
  CRITICAL = 'critical',
  FAILURE = 'failure',
  UNKNOWN = 'unknown'
}

/**
 * 维护类型枚举
 */
export enum MaintenanceType {
  PREVENTIVE = 'preventive',
  PREDICTIVE = 'predictive',
  CORRECTIVE = 'corrective',
  EMERGENCY = 'emergency',
  CONDITION_BASED = 'condition_based'
}

/**
 * 维护状态枚举
 */
export enum MaintenanceStatus {
  SCHEDULED = 'scheduled',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  OVERDUE = 'overdue'
}

/**
 * 故障严重程度枚举
 */
export enum FailureSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

/**
 * 设备状态监控数据接口
 */
export interface ConditionMonitoringData {
  deviceId: string;
  timestamp: Date;
  parameters: {
    name: string;
    value: number;
    unit: string;
    normalRange: {
      min: number;
      max: number;
    };
    warningRange: {
      min: number;
      max: number;
    };
    criticalRange: {
      min: number;
      max: number;
    };
    status: HealthStatus;
    trend: 'stable' | 'increasing' | 'decreasing';
  }[];
  overallHealth: HealthStatus;
  healthScore: number; // 0-100
  anomalies: {
    parameter: string;
    severity: FailureSeverity;
    description: string;
    recommendation: string;
  }[];
}

/**
 * 故障预测结果接口
 */
export interface FailurePrediction {
  id: string;
  deviceId: string;
  predictionDate: Date;
  predictedFailureDate: Date;
  confidence: number; // 0-1
  failureType: string;
  severity: FailureSeverity;
  description: string;
  rootCause: string;
  recommendations: string[];
  preventiveActions: {
    action: string;
    priority: number;
    estimatedCost: number;
    estimatedTime: number;
  }[];
  riskScore: number; // 0-100
}

/**
 * 维护任务接口
 */
export interface MaintenanceTask {
  id: string;
  deviceId: string;
  type: MaintenanceType;
  title: string;
  description: string;
  priority: number; // 1-10
  status: MaintenanceStatus;
  scheduledDate: Date;
  estimatedDuration: number; // 分钟
  actualStartTime?: Date;
  actualEndTime?: Date;
  assignedTechnician?: string;
  requiredSkills: string[];
  requiredParts: {
    partNumber: string;
    description: string;
    quantity: number;
    cost: number;
    availability: boolean;
  }[];
  requiredTools: string[];
  instructions: string[];
  safetyNotes: string[];
  completionNotes?: string;
  cost?: number;
}

/**
 * 零件更换记录接口
 */
export interface PartReplacementRecord {
  id: string;
  deviceId: string;
  maintenanceTaskId: string;
  partNumber: string;
  partDescription: string;
  oldPartSerialNumber?: string;
  newPartSerialNumber: string;
  replacementDate: Date;
  technician: string;
  reason: string;
  cost: number;
  warranty: {
    startDate: Date;
    endDate: Date;
    provider: string;
  };
  notes?: string;
}

/**
 * 维护历史记录接口
 */
export interface MaintenanceHistory {
  deviceId: string;
  totalMaintenanceCount: number;
  lastMaintenanceDate?: Date;
  nextScheduledMaintenance?: Date;
  averageMaintenanceInterval: number; // 天
  totalDowntime: number; // 小时
  totalMaintenanceCost: number;
  maintenanceRecords: {
    date: Date;
    type: MaintenanceType;
    duration: number;
    cost: number;
    technician: string;
    description: string;
    partsReplaced: string[];
  }[];
  trends: {
    failureRate: number; // 故障率趋势
    maintenanceCost: number; // 维护成本趋势
    downtime: number; // 停机时间趋势
    reliability: number; // 可靠性趋势
  };
}

/**
 * 预测性维护管理器
 */
class PredictiveMaintenanceManager {
  private monitoringData: Map<string, ConditionMonitoringData[]> = new Map();
  private predictions: Map<string, FailurePrediction[]> = new Map();
  private maintenanceTasks: Map<string, MaintenanceTask> = new Map();
  private partReplacements: Map<string, PartReplacementRecord[]> = new Map();
  private maintenanceHistory: Map<string, MaintenanceHistory> = new Map();
  private eventListeners: Map<string, Function[]> = new Map();

  /**
   * 添加状态监控数据
   */
  addConditionMonitoringData(data: ConditionMonitoringData): void {
    const deviceId = data.deviceId;
    
    if (!this.monitoringData.has(deviceId)) {
      this.monitoringData.set(deviceId, []);
    }
    
    const deviceData = this.monitoringData.get(deviceId)!;
    deviceData.push(data);
    
    // 保留最近1000条记录
    if (deviceData.length > 1000) {
      deviceData.splice(0, deviceData.length - 1000);
    }
    
    this.emit('conditionDataAdded', { deviceId, data });
    
    // 检查是否需要触发预警
    this.checkForAnomalies(data);
    
    Debug.log('PredictiveMaintenanceManager', `状态监控数据添加: ${deviceId}, 健康状态: ${data.overallHealth}`);
  }

  /**
   * 获取最新状态监控数据
   */
  getLatestConditionData(deviceId: string): ConditionMonitoringData | undefined {
    const deviceData = this.monitoringData.get(deviceId);
    return deviceData && deviceData.length > 0 ? deviceData[deviceData.length - 1] : undefined;
  }

  /**
   * 获取设备状态监控历史
   */
  getConditionHistory(deviceId: string, limit?: number): ConditionMonitoringData[] {
    const deviceData = this.monitoringData.get(deviceId) || [];
    return limit ? deviceData.slice(-limit) : deviceData;
  }

  /**
   * 创建故障预测
   */
  createFailurePrediction(prediction: Omit<FailurePrediction, 'id'>): FailurePrediction {
    const predictionId = this.generatePredictionId();
    const fullPrediction: FailurePrediction = {
      id: predictionId,
      ...prediction
    };

    const deviceId = prediction.deviceId;
    if (!this.predictions.has(deviceId)) {
      this.predictions.set(deviceId, []);
    }
    
    this.predictions.get(deviceId)!.push(fullPrediction);
    this.emit('failurePredicted', { prediction: fullPrediction });
    
    Debug.log('PredictiveMaintenanceManager', `故障预测创建: ${deviceId}, 预测日期: ${prediction.predictedFailureDate.toISOString()}`);
    return fullPrediction;
  }

  /**
   * 获取设备故障预测
   */
  getFailurePredictions(deviceId: string): FailurePrediction[] {
    return this.predictions.get(deviceId) || [];
  }

  /**
   * 获取活跃的故障预测
   */
  getActivePredictions(deviceId: string): FailurePrediction[] {
    const predictions = this.predictions.get(deviceId) || [];
    const now = new Date();
    return predictions.filter(p => p.predictedFailureDate > now);
  }

  /**
   * 检查异常情况
   */
  private checkForAnomalies(data: ConditionMonitoringData): void {
    if (data.overallHealth === HealthStatus.CRITICAL || data.overallHealth === HealthStatus.WARNING) {
      this.emit('anomalyDetected', { deviceId: data.deviceId, data });
    }
    
    // 检查是否有新的异常
    if (data.anomalies.length > 0) {
      data.anomalies.forEach(anomaly => {
        if (anomaly.severity === FailureSeverity.CRITICAL || anomaly.severity === FailureSeverity.HIGH) {
          this.emit('criticalAnomalyDetected', { deviceId: data.deviceId, anomaly });
        }
      });
    }
  }

  /**
   * 生成预测ID
   */
  private generatePredictionId(): string {
    return 'PRED_' + Date.now().toString(36) + '_' + Math.random().toString(36).substr(2, 5);
  }

  /**
   * 生成任务ID
   */
  generateTaskId(): string {
    return 'TASK_' + Date.now().toString(36) + '_' + Math.random().toString(36).substr(2, 5);
  }

  // 事件系统
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  off(event: string, callback: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  private emit(event: string, data?: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          Debug.error('PredictiveMaintenanceManager', `事件回调执行失败: ${event}`, error);
        }
      });
    }
  }
}

// 全局预测性维护管理器实例
const predictiveMaintenanceManager = new PredictiveMaintenanceManager();

/**
 * 状态监控节点
 */
export class ConditionMonitoringNode extends VisualScriptNode {
  public static readonly TYPE = 'ConditionMonitoring';
  public static readonly NAME = '状态监控';
  public static readonly DESCRIPTION = '监控设备状态和健康参数';

  constructor(nodeType: string = ConditionMonitoringNode.TYPE, name: string = ConditionMonitoringNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('addData', 'trigger', '添加监控数据');
    this.addInput('getLatest', 'trigger', '获取最新数据');
    this.addInput('getHistory', 'trigger', '获取历史数据');
    this.addInput('deviceId', 'string', '设备ID');
    this.addInput('parameters', 'array', '监控参数');
    this.addInput('healthScore', 'number', '健康评分');
    this.addInput('overallHealth', 'string', '整体健康状态');
    this.addInput('limit', 'number', '历史记录限制');

    // 输出端口
    this.addOutput('monitoringData', 'object', '监控数据');
    this.addOutput('healthStatus', 'string', '健康状态');
    this.addOutput('healthScore', 'number', '健康评分');
    this.addOutput('anomalies', 'array', '异常列表');
    this.addOutput('history', 'array', '历史数据');
    this.addOutput('onDataAdded', 'trigger', '数据添加完成');
    this.addOutput('onAnomalyDetected', 'trigger', '检测到异常');
    this.addOutput('onCriticalAlert', 'trigger', '严重告警');
    this.addOutput('onError', 'trigger', '操作失败');
  }

  public async execute(inputs?: any): Promise<any> {
    try {
      const addDataTrigger = inputs?.addData;
      const getLatestTrigger = inputs?.getLatest;
      const getHistoryTrigger = inputs?.getHistory;

      if (addDataTrigger) {
        return this.addMonitoringData(inputs);
      } else if (getLatestTrigger) {
        return this.getLatestData(inputs);
      } else if (getHistoryTrigger) {
        return this.getHistoryData(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('ConditionMonitoringNode', '状态监控操作失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private addMonitoringData(inputs: any): any {
    const deviceId = inputs?.deviceId as string;
    const parameters = inputs?.parameters as any[] || [];
    const healthScore = inputs?.healthScore as number || 100;
    const overallHealth = inputs?.overallHealth as HealthStatus || HealthStatus.HEALTHY;

    if (!deviceId) {
      throw new Error('未提供设备ID');
    }

    const monitoringData: ConditionMonitoringData = {
      deviceId,
      timestamp: new Date(),
      parameters: parameters.map(param => ({
        name: param.name || '',
        value: param.value || 0,
        unit: param.unit || '',
        normalRange: param.normalRange || { min: 0, max: 100 },
        warningRange: param.warningRange || { min: 0, max: 100 },
        criticalRange: param.criticalRange || { min: 0, max: 100 },
        status: this.determineParameterStatus(param.value, param.normalRange, param.warningRange, param.criticalRange),
        trend: param.trend || 'stable'
      })),
      overallHealth,
      healthScore,
      anomalies: this.detectAnomalies(parameters)
    };

    predictiveMaintenanceManager.addConditionMonitoringData(monitoringData);

    const hasAnomalies = monitoringData.anomalies.length > 0;
    const hasCriticalAnomalies = monitoringData.anomalies.some(a => a.severity === FailureSeverity.CRITICAL);

    Debug.log('ConditionMonitoringNode', `监控数据添加: ${deviceId}, 健康评分: ${healthScore}`);

    return {
      monitoringData,
      healthStatus: overallHealth,
      healthScore,
      anomalies: monitoringData.anomalies,
      history: [],
      onDataAdded: true,
      onAnomalyDetected: hasAnomalies,
      onCriticalAlert: hasCriticalAnomalies,
      onError: false
    };
  }

  private getLatestData(inputs: any): any {
    const deviceId = inputs?.deviceId as string;

    if (!deviceId) {
      throw new Error('未提供设备ID');
    }

    const latestData = predictiveMaintenanceManager.getLatestConditionData(deviceId);
    if (!latestData) {
      throw new Error('未找到监控数据');
    }

    return {
      monitoringData: latestData,
      healthStatus: latestData.overallHealth,
      healthScore: latestData.healthScore,
      anomalies: latestData.anomalies,
      history: [],
      onDataAdded: false,
      onAnomalyDetected: false,
      onCriticalAlert: false,
      onError: false
    };
  }

  private getHistoryData(inputs: any): any {
    const deviceId = inputs?.deviceId as string;
    const limit = inputs?.limit as number;

    if (!deviceId) {
      throw new Error('未提供设备ID');
    }

    const history = predictiveMaintenanceManager.getConditionHistory(deviceId, limit);

    Debug.log('ConditionMonitoringNode', `获取历史数据: ${deviceId}, ${history.length} 条记录`);

    return {
      monitoringData: history.length > 0 ? history[history.length - 1] : null,
      healthStatus: history.length > 0 ? history[history.length - 1].overallHealth : '',
      healthScore: history.length > 0 ? history[history.length - 1].healthScore : 0,
      anomalies: [],
      history,
      onDataAdded: false,
      onAnomalyDetected: false,
      onCriticalAlert: false,
      onError: false
    };
  }

  private determineParameterStatus(value: number, normalRange: any, warningRange: any, criticalRange: any): HealthStatus {
    if (value >= criticalRange.min && value <= criticalRange.max) {
      return HealthStatus.CRITICAL;
    } else if (value >= warningRange.min && value <= warningRange.max) {
      return HealthStatus.WARNING;
    } else if (value >= normalRange.min && value <= normalRange.max) {
      return HealthStatus.HEALTHY;
    } else {
      return HealthStatus.UNKNOWN;
    }
  }

  private detectAnomalies(parameters: any[]): any[] {
    const anomalies: any[] = [];

    parameters.forEach(param => {
      const status = this.determineParameterStatus(
        param.value,
        param.normalRange,
        param.warningRange,
        param.criticalRange
      );

      if (status === HealthStatus.WARNING || status === HealthStatus.CRITICAL) {
        anomalies.push({
          parameter: param.name,
          severity: status === HealthStatus.CRITICAL ? FailureSeverity.CRITICAL : FailureSeverity.MEDIUM,
          description: `参数 ${param.name} 超出正常范围: ${param.value} ${param.unit}`,
          recommendation: status === HealthStatus.CRITICAL ? '立即检查设备' : '建议安排检查'
        });
      }
    });

    return anomalies;
  }

  private getDefaultOutputs(): any {
    return {
      monitoringData: null,
      healthStatus: '',
      healthScore: 0,
      anomalies: [],
      history: [],
      onDataAdded: false,
      onAnomalyDetected: false,
      onCriticalAlert: false,
      onError: false
    };
  }
}

/**
 * 故障预测节点
 */
export class FailurePredictionNode extends VisualScriptNode {
  public static readonly TYPE = 'FailurePrediction';
  public static readonly NAME = '故障预测';
  public static readonly DESCRIPTION = '基于历史数据和当前状态预测设备故障';

  constructor(nodeType: string = FailurePredictionNode.TYPE, name: string = FailurePredictionNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('createPrediction', 'trigger', '创建预测');
    this.addInput('getPredictions', 'trigger', '获取预测');
    this.addInput('analyzeTrends', 'trigger', '分析趋势');
    this.addInput('deviceId', 'string', '设备ID');
    this.addInput('historicalData', 'array', '历史数据');
    this.addInput('currentCondition', 'object', '当前状态');
    this.addInput('predictionHorizon', 'number', '预测时间范围(天)');
    this.addInput('confidenceThreshold', 'number', '置信度阈值');

    // 输出端口
    this.addOutput('prediction', 'object', '故障预测');
    this.addOutput('predictions', 'array', '预测列表');
    this.addOutput('riskScore', 'number', '风险评分');
    this.addOutput('timeToFailure', 'number', '预计故障时间(天)');
    this.addOutput('confidence', 'number', '预测置信度');
    this.addOutput('recommendations', 'array', '建议措施');
    this.addOutput('trends', 'object', '趋势分析');
    this.addOutput('onPredictionCreated', 'trigger', '预测创建完成');
    this.addOutput('onHighRisk', 'trigger', '高风险预警');
    this.addOutput('onCriticalRisk', 'trigger', '严重风险预警');
    this.addOutput('onError', 'trigger', '操作失败');
  }

  public async execute(inputs?: any): Promise<any> {
    try {
      const createPredictionTrigger = inputs?.createPrediction;
      const getPredictionsTrigger = inputs?.getPredictions;
      const analyzeTrendsTrigger = inputs?.analyzeTrends;

      if (createPredictionTrigger) {
        return this.createFailurePrediction(inputs);
      } else if (getPredictionsTrigger) {
        return this.getFailurePredictions(inputs);
      } else if (analyzeTrendsTrigger) {
        return this.analyzeTrends(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('FailurePredictionNode', '故障预测操作失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private createFailurePrediction(inputs: any): any {
    const deviceId = inputs?.deviceId as string;
    const historicalData = inputs?.historicalData as any[] || [];
    const currentCondition = inputs?.currentCondition as any || {};
    const predictionHorizon = inputs?.predictionHorizon as number || 30;
    const confidenceThreshold = inputs?.confidenceThreshold as number || 0.7;

    if (!deviceId) {
      throw new Error('未提供设备ID');
    }

    // 执行故障预测分析
    const predictionResult = this.performFailurePrediction(
      deviceId,
      historicalData,
      currentCondition,
      predictionHorizon
    );

    // 创建预测记录
    const prediction = predictiveMaintenanceManager.createFailurePrediction({
      deviceId,
      predictionDate: new Date(),
      predictedFailureDate: predictionResult.predictedFailureDate,
      confidence: predictionResult.confidence,
      failureType: predictionResult.failureType,
      severity: predictionResult.severity,
      description: predictionResult.description,
      rootCause: predictionResult.rootCause,
      recommendations: predictionResult.recommendations,
      preventiveActions: predictionResult.preventiveActions,
      riskScore: predictionResult.riskScore
    });

    const isHighRisk = predictionResult.riskScore >= 70;
    const isCriticalRisk = predictionResult.riskScore >= 90;

    Debug.log('FailurePredictionNode', `故障预测创建: ${deviceId}, 风险评分: ${predictionResult.riskScore}`);

    return {
      prediction,
      predictions: [],
      riskScore: predictionResult.riskScore,
      timeToFailure: predictionResult.timeToFailure,
      confidence: predictionResult.confidence,
      recommendations: predictionResult.recommendations,
      trends: null,
      onPredictionCreated: true,
      onHighRisk: isHighRisk,
      onCriticalRisk: isCriticalRisk,
      onError: false
    };
  }

  private getFailurePredictions(inputs: any): any {
    const deviceId = inputs?.deviceId as string;

    if (!deviceId) {
      throw new Error('未提供设备ID');
    }

    const predictions = predictiveMaintenanceManager.getFailurePredictions(deviceId);
    const activePredictions = predictiveMaintenanceManager.getActivePredictions(deviceId);

    const latestPrediction = predictions.length > 0 ? predictions[predictions.length - 1] : null;

    Debug.log('FailurePredictionNode', `获取预测: ${deviceId}, ${predictions.length} 个预测记录`);

    return {
      prediction: latestPrediction,
      predictions: activePredictions,
      riskScore: latestPrediction?.riskScore || 0,
      timeToFailure: latestPrediction ? this.calculateTimeToFailure(latestPrediction.predictedFailureDate) : 0,
      confidence: latestPrediction?.confidence || 0,
      recommendations: latestPrediction?.recommendations || [],
      trends: null,
      onPredictionCreated: false,
      onHighRisk: false,
      onCriticalRisk: false,
      onError: false
    };
  }

  private analyzeTrends(inputs: any): any {
    const deviceId = inputs?.deviceId as string;
    const historicalData = inputs?.historicalData as any[] || [];

    if (!deviceId) {
      throw new Error('未提供设备ID');
    }

    const trends = this.performTrendAnalysis(historicalData);

    Debug.log('FailurePredictionNode', `趋势分析完成: ${deviceId}`);

    return {
      prediction: null,
      predictions: [],
      riskScore: 0,
      timeToFailure: 0,
      confidence: 0,
      recommendations: [],
      trends,
      onPredictionCreated: false,
      onHighRisk: false,
      onCriticalRisk: false,
      onError: false
    };
  }

  private performFailurePrediction(
    deviceId: string,
    historicalData: any[],
    currentCondition: any,
    predictionHorizon: number
  ): any {
    // 简化的故障预测算法
    const baseRiskScore = Math.random() * 100;
    const healthScore = currentCondition.healthScore || 100;

    // 根据健康评分调整风险
    const healthRiskFactor = (100 - healthScore) / 100;
    const adjustedRiskScore = Math.min(100, baseRiskScore + (healthRiskFactor * 50));

    // 预测故障时间
    const timeToFailure = Math.max(1, predictionHorizon * (1 - adjustedRiskScore / 100));
    const predictedFailureDate = new Date();
    predictedFailureDate.setDate(predictedFailureDate.getDate() + timeToFailure);

    // 确定故障类型和严重程度
    const failureTypes = ['机械磨损', '电气故障', '传感器故障', '软件异常', '过热'];
    const failureType = failureTypes[Math.floor(Math.random() * failureTypes.length)];

    let severity: FailureSeverity;
    if (adjustedRiskScore >= 90) severity = FailureSeverity.CRITICAL;
    else if (adjustedRiskScore >= 70) severity = FailureSeverity.HIGH;
    else if (adjustedRiskScore >= 40) severity = FailureSeverity.MEDIUM;
    else severity = FailureSeverity.LOW;

    return {
      riskScore: Math.round(adjustedRiskScore),
      timeToFailure: Math.round(timeToFailure),
      confidence: 0.7 + Math.random() * 0.3,
      predictedFailureDate,
      failureType,
      severity,
      description: `预测设备可能在${Math.round(timeToFailure)}天内发生${failureType}`,
      rootCause: this.generateRootCause(failureType),
      recommendations: this.generateRecommendations(severity, failureType),
      preventiveActions: this.generatePreventiveActions(severity, failureType)
    };
  }

  private performTrendAnalysis(historicalData: any[]): any {
    if (historicalData.length === 0) {
      return {
        degradationRate: 0,
        performanceTrend: 'stable',
        anomalyFrequency: 0,
        maintenanceEffectiveness: 0
      };
    }

    // 简化的趋势分析
    return {
      degradationRate: Math.random() * 5,
      performanceTrend: Math.random() > 0.5 ? 'improving' : 'degrading',
      anomalyFrequency: Math.random() * 10,
      maintenanceEffectiveness: 70 + Math.random() * 30
    };
  }

  private generateRootCause(failureType: string): string {
    const rootCauses: Record<string, string[]> = {
      '机械磨损': ['长期使用导致部件磨损', '润滑不足', '负载过重'],
      '电气故障': ['电压不稳定', '接触不良', '绝缘老化'],
      '传感器故障': ['传感器老化', '环境干扰', '校准偏差'],
      '软件异常': ['程序错误', '配置问题', '版本兼容性'],
      '过热': ['散热不良', '环境温度过高', '负载过重']
    };

    const causes = rootCauses[failureType] || ['未知原因'];
    return causes[Math.floor(Math.random() * causes.length)];
  }

  private generateRecommendations(severity: FailureSeverity, failureType: string): string[] {
    const baseRecommendations = [
      '增加监控频率',
      '安排专业检查',
      '准备备用零件'
    ];

    if (severity === FailureSeverity.CRITICAL) {
      baseRecommendations.unshift('立即停机检查', '联系技术支持');
    } else if (severity === FailureSeverity.HIGH) {
      baseRecommendations.unshift('尽快安排维护');
    }

    return baseRecommendations;
  }

  private generatePreventiveActions(severity: FailureSeverity, failureType: string): any[] {
    return [
      {
        action: '预防性维护',
        priority: severity === FailureSeverity.CRITICAL ? 1 : 3,
        estimatedCost: 1000 + Math.random() * 5000,
        estimatedTime: 2 + Math.random() * 8
      },
      {
        action: '零件更换',
        priority: severity === FailureSeverity.CRITICAL ? 2 : 5,
        estimatedCost: 500 + Math.random() * 2000,
        estimatedTime: 1 + Math.random() * 4
      }
    ];
  }

  private calculateTimeToFailure(predictedFailureDate: Date): number {
    const now = new Date();
    const timeDiff = predictedFailureDate.getTime() - now.getTime();
    return Math.max(0, Math.round(timeDiff / (1000 * 60 * 60 * 24)));
  }

  private getDefaultOutputs(): any {
    return {
      prediction: null,
      predictions: [],
      riskScore: 0,
      timeToFailure: 0,
      confidence: 0,
      recommendations: [],
      trends: null,
      onPredictionCreated: false,
      onHighRisk: false,
      onCriticalRisk: false,
      onError: false
    };
  }
}
