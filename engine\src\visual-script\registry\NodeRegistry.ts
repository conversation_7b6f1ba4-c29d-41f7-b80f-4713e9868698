/**
 * 视觉脚本节点注册系统
 * 统一管理所有节点的注册、分类和编辑器集成
 */
import { VisualScriptNode } from '../../visualscript/VisualScriptNode';
import { Debug } from '../../utils/Debug';

// 导入所有节点类
// 基础功能节点
// 动作捕捉节点
import { CameraInputNode } from '../nodes/mocap/CameraInputNode';
import { PoseDetectionNode } from '../nodes/mocap/PoseDetectionNode';
import { HandTrackingNode } from '../nodes/mocap/HandTrackingNode';
import { VirtualInteractionNode } from '../nodes/mocap/VirtualInteractionNode';
import { FaceDetectionNode } from '../nodes/mocap/FaceDetectionNode';
import { EyeTrackingNode } from '../nodes/mocap/EyeTrackingNode';

// 高级输入节点
import {
  MultiTouchNode,
  GestureRecognitionNode,
  VoiceInputNode,
  MotionSensorNode
} from '../nodes/input/AdvancedInputNodes';

// 传感器输入节点
import {
  AccelerometerNode,
  GyroscopeNode,
  CompassNode,
  ProximityNode,
  LightSensorNode,
  PressureSensorNode
} from '../nodes/input/SensorInputNodes';

// VR/AR输入节点
import {
  VRControllerInputNode,
  VRHeadsetTrackingNode,
  ARTouchInputNode,
  ARGestureInputNode,
  SpatialInputNode
} from '../nodes/input/VRARInputNodes';

// VR/AR高级输入节点
import {
  EyeTrackingInputNode,
  HandTrackingInputNode,
  VoiceCommandInputNode
} from '../nodes/input/VRARAdvancedNodes';

// 实体节点
import {
  CreateEntityNode,
  DestroyEntityNode,
  FindEntityNode,
  CloneEntityNode,
  EntityActiveNode
} from '../nodes/entity/EntityNodes';

// 组件节点
import {
  AddComponentNode,
  RemoveComponentNode,
  GetComponentNode,
  ComponentEnabledNode,
  GetAllComponentsNode,
  ComponentPropertyNode
} from '../nodes/entity/ComponentNodes';

// 变换节点
import {
  SetPositionNode,
  GetPositionNode,
  SetRotationNode,
  GetRotationNode,
  SetScaleNode,
  GetScaleNode,
  MoveNode,
  RotateNode
} from '../nodes/entity/TransformNodes';

// 物理节点
import {
  AddRigidBodyNode,
  AddColliderNode,
  ApplyForceNode,
  SetVelocityNode,
  CollisionDetectionNode,
  RaycastNode
} from '../nodes/physics/PhysicsNodes';

// 动画节点
import {
  TweenNode,
  KeyframeAnimationNode
} from '../nodes/animation/AnimationNodes';

import {
  KeyboardInputNode,
  MouseInputNode,
  TouchInputNode,
  GamepadInputNode
} from '../nodes/input/InputNodes';

// 音频节点
import {
  LoadAudioNode,
  PlayAudioNode,
  AudioListenerNode
} from '../nodes/audio/AudioNodes';

// 3D世界构建节点
import {
  AutoSceneGenerationNode,
  SceneLayoutNode
} from '../nodes/scene/SceneGenerationNodes';

import {
  CreateWaterBodyNode,
  WaterWaveNode
} from '../nodes/water/WaterSystemNodes';

// 粒子系统节点
import {
  ParticleEmitterNode,
  ParticleEffectNode
} from '../nodes/particles/ParticleSystemNodes';

// 后处理节点
import {
  PostProcessEffectNode,
  ToneMappingNode
} from '../nodes/postprocessing/PostProcessingNodes';

import {
  TerrainGenerationNode,
  TerrainErosionNode
} from '../nodes/terrain/TerrainSystemNodes';

// 专业系统节点
import {
  WalletConnectNode,
  SmartContractNode,
  NFTOperationNode
} from '../nodes/blockchain/BlockchainNodes';

import {
  LearningRecordNode,
  LearningStatisticsNode,
  AchievementSystemNode
} from '../nodes/learning/LearningRecordNodes';

import {
  CreateUIElementNode,
  UILayoutNode,
  UIEventHandlerNode
} from '../nodes/ui/UINodes';

// AI增强节点
import {
  KnowledgeBaseNode,
  RAGQueryNode,
  DocumentProcessingNode,
  SemanticSearchNode
} from '../nodes/rag/RAGApplicationNodes';

import {
  GISAnalysisNode,
  SpatialQueryNode,
  GeospatialVisualizationNode,
  LocationServicesNode
} from '../nodes/spatial/SpatialInformationNodes';

// 新增的核心节点
import {
  WebSocketNode,
  WebRTCNode,
  HTTPRequestNode,
  NetworkSyncNode
} from '../nodes/network/NetworkNodes';

import {
  MaterialSystemNode,
  LightControlNode,
  CameraManagerNode,
  RenderConfigNode,
  CreateMaterialNode,
  SetMaterialPropertyNode,
  GetMaterialPropertyNode,
  MaterialBlendNode,
  MaterialAnimationNode,
  MaterialOptimizationNode,
  PBRMaterialNode,
  StandardMaterialNode,
  CustomMaterialNode,
  MaterialPresetNode
} from '../nodes/rendering/RenderingNodes';

// 光照控制节点
import {
  CreateLightNode,
  SetLightPropertyNode,
  LightAnimationNode
} from '../nodes/rendering/LightingNodes';

// 相机管理节点
import {
  CreateCameraNode
} from '../nodes/rendering/CameraNodes';

// 场景管理节点
import {
  LoadSceneNode,
  SaveSceneNode,
  CreateSceneNode,
  DestroySceneNode,
  AddObjectToSceneNode,
  RemoveObjectFromSceneNode,
  FindSceneObjectNode
} from '../nodes/scene/SceneManagementNodes';

// 场景切换节点
import {
  SceneTransitionNode
} from '../nodes/scene/SceneTransitionNodes';

import {
  AnimationStateMachineNode,
  AnimationBlendNode,
  IKSystemNode,
  AnimationEventNode
} from '../nodes/animation/AdvancedAnimationNodes';

// 第六阶段新增节点
import {
  DeviceManagerNode,
  DataCollectionNode,
  QualityInspectionNode,
  AlarmSystemNode,
  ProcessControlNode
} from '../nodes/industrial/IndustrialAutomationNodes';

// 高级音频节点
import {
  SpatialAudioNode,
  AudioFilterNode,
  AudioEffectNode
} from '../nodes/audio/AdvancedAudioNodes';

// 项目管理节点
import {
  CreateProjectNode,
  LoadProjectNode,
  SaveProjectNode,
  ProjectVersionNode,
  ProjectCollaborationNode,
  ProjectPermissionNode,
  ProjectBackupNode,
  ProjectAnalyticsNode,
  ProjectTemplateNode,
  ProjectExportNode
} from '../nodes/project/ProjectManagementNodes';

// AI服务节点
import {
  AIModelLoadNode,
  AIInferenceNode,
  AITrainingNode,
  NLPProcessingNode,
  ComputerVisionNode,
  SpeechRecognitionNode,
  SentimentAnalysisNode,
  RecommendationNode,
  ChatbotNode,
  AIOptimizationNode,
  AIMonitoringNode,
  AIModelVersionNode,
  AIDataPreprocessingNode,
  AIResultPostprocessingNode,
  AIPerformanceNode
} from '../nodes/ai/AIServiceNodes';

import {
  VRControllerNode,
  GestureRecognitionNode as VRGestureRecognitionNode,
  VoiceRecognitionNode
} from '../nodes/input/VRInputNodes';

// 批次2.3：工业制造节点
// MES系统节点
import {
  ProductionOrderNode,
  WorkflowManagementNode,
  QualityControlNode
} from '../nodes/industrial/MESSystemNodes';

// 设备管理节点
import {
  DeviceConnectionNode,
  DeviceMonitoringNode,
  DeviceControlNode
} from '../nodes/industrial/DeviceManagementNodes';

// 预测性维护节点
import {
  ConditionMonitoringNode,
  FailurePredictionNode
} from '../nodes/industrial/PredictiveMaintenanceNodes';

import {
  LODSystemNode,
  BatchRenderingNode,
  InstancedRenderingNode,
  FrustumCullingNode
} from '../nodes/rendering/RenderingOptimizationNodes';

import {
  ClothSystemNode
} from '../nodes/physics/SoftBodyNodes';

// 批次1.5：物理系统增强节点
import {
  SoftBodyPhysicsNode,
  FluidSimulationNode,
  ClothSimulationNode,
  RopeSimulationNode,
  DestructionNode,
  PhysicsConstraintNode
} from '../nodes/physics/AdvancedPhysicsNodes';

import {
  PhysicsOptimizationNode,
  PhysicsLODNode,
  PhysicsPerformanceMonitorNode,
  PhysicsBatchingNode
} from '../nodes/physics/PhysicsOptimizationNodes';

// 批次1.6：音频系统增强节点
import {
  AudioMixerNode,
  AudioEffectChainNode,
  AudioReverbNode,
  AudioEQNode
} from '../nodes/audio/AdvancedAudioSystemNodes';

import {
  AudioOptimizationNode,
  AudioStreamingNode
} from '../nodes/audio/AudioOptimizationNodes';

import {
  ObjectDetectionNode,
  ImageClassificationNode,
  FeatureExtractionNode
} from '../nodes/ai/ComputerVisionNodes';

// 资源管理节点 - 批次1.3
import {
  LoadAssetNode,
  UnloadAssetNode,
  PreloadAssetNode,
  AsyncLoadAssetNode,
  LoadAssetBundleNode,
  AssetDependencyNode,
  AssetCacheNode,
  AssetCompressionNode,
  AssetEncryptionNode,
  AssetValidationNode,
  AssetMetadataNode,
  AssetVersionNode,
  AssetOptimizationNode,
  TextureCompressionNode,
  MeshOptimizationNode,
  AudioCompressionNode,
  AssetBatchingNode,
  AssetStreamingNode,
  AssetMemoryManagementNode,
  AssetGarbageCollectionNode,
  AssetPerformanceMonitorNode,
  AssetUsageAnalyticsNode
} from '../nodes/resources';

// 批次2.1 - 数据服务节点
import {
  DatabaseConnectionNode,
  DatabaseQueryNode,
  DatabaseInsertNode,
  DatabaseUpdateNode,
  DatabaseDeleteNode,
  DatabaseTransactionNode
} from '../nodes/data/DataServiceNodes';

import {
  DataValidationNode,
  DataTransformationNode,
  DataAggregationNode
} from '../nodes/data/DataServiceNodes2';

import {
  DataBackupNode,
  DataSyncNode,
  DataAnalyticsNode
} from '../nodes/data/DataServiceNodes3';

// 批次2.1 - 认证授权节点
import {
  JWTTokenNode,
  OAuth2Node,
  RBACNode
} from '../nodes/auth/AuthenticationNodes';

import {
  PermissionCheckNode,
  SecurityAuditNode,
  EncryptionNode,
  DecryptionNode,
  SecurityMonitoringNode
} from '../nodes/auth/AuthenticationNodes2';

// 批次2.1 - 文件服务节点
import {
  FileUploadNode,
  FileDownloadNode,
  FileStorageNode
} from '../nodes/file/FileServiceNodes';

import {
  FileCompressionNode,
  FileEncryptionNode,
  FileVersioningNode
} from '../nodes/file/FileServiceNodes2';

import {
  FileMetadataNode,
  FileSearchNode,
  FileSyncNode,
  FileAnalyticsNode
} from '../nodes/file/FileServiceNodes3';

/**
 * 节点分类枚举
 */
export enum NodeCategory {
  MOTION_CAPTURE = 'motion_capture',
  ENTITY_MANAGEMENT = 'entity_management',
  COMPONENT_MANAGEMENT = 'component_management',
  TRANSFORM = 'transform',
  PHYSICS = 'physics',
  ANIMATION = 'animation',
  INPUT = 'input',
  ADVANCED_INPUT = 'advanced_input',
  SENSOR_INPUT = 'sensor_input',
  VR_AR_INPUT = 'vr_ar_input',
  AUDIO = 'audio',
  SCENE_GENERATION = 'scene_generation',
  WATER_SYSTEM = 'water_system',
  PARTICLE_SYSTEM = 'particle_system',
  POST_PROCESS = 'post_process',
  TERRAIN_SYSTEM = 'terrain_system',
  BLOCKCHAIN = 'blockchain',
  LEARNING_RECORD = 'learning_record',
  UI_INTERFACE = 'ui_interface',
  RAG_APPLICATION = 'rag_application',
  SPATIAL_INFORMATION = 'spatial_information',
  NETWORK = 'network',
  RENDERING = 'rendering',
  ADVANCED_ANIMATION = 'advanced_animation',
  INDUSTRIAL_AUTOMATION = 'industrial_automation',
  ADVANCED_AUDIO = 'advanced_audio',
  VR_INPUT = 'vr_input',
  RENDERING_OPTIMIZATION = 'rendering_optimization',
  SOFT_BODY_PHYSICS = 'soft_body_physics',
  COMPUTER_VISION = 'computer_vision',
  SCENE_MANAGEMENT = 'scene_management',
  RESOURCE_MANAGEMENT = 'resource_management',
  RESOURCE_OPTIMIZATION = 'resource_optimization',
  PROJECT_MANAGEMENT = 'project_management',
  AI_SERVICE = 'ai_service',
  DATA_SERVICE = 'data_service',
  AUTHENTICATION = 'authentication',
  FILE_SERVICE = 'file_service'
}

/**
 * 节点信息接口
 */
export interface NodeInfo {
  type: string;
  name: string;
  description: string;
  category: NodeCategory;
  nodeClass: new (nodeType: string, name: string, id?: string) => VisualScriptNode;
  icon?: string;
  color?: string;
  tags?: string[];
  deprecated?: boolean;
  experimental?: boolean;
}

/**
 * 节点注册表
 */
class NodeRegistryManager {
  private nodes: Map<string, NodeInfo> = new Map();
  private categories: Map<NodeCategory, NodeInfo[]> = new Map();
  private initialized: boolean = false;

  /**
   * 初始化节点注册表
   */
  initialize(): void {
    if (this.initialized) {
      return;
    }

    this.registerAllNodes();
    this.buildCategoryIndex();
    this.initialized = true;

    Debug.log('NodeRegistry', `节点注册完成: ${this.nodes.size}个节点, ${this.categories.size}个分类`);
  }

  /**
   * 注册所有节点
   */
  private registerAllNodes(): void {
    // 动作捕捉节点
    this.registerNode({
      type: CameraInputNode.TYPE,
      name: CameraInputNode.NAME,
      description: CameraInputNode.DESCRIPTION,
      category: NodeCategory.MOTION_CAPTURE,
      nodeClass: CameraInputNode,
      icon: 'camera',
      color: '#FF6B6B',
      tags: ['camera', 'input', 'video']
    });

    this.registerNode({
      type: PoseDetectionNode.TYPE,
      name: PoseDetectionNode.NAME,
      description: PoseDetectionNode.DESCRIPTION,
      category: NodeCategory.MOTION_CAPTURE,
      nodeClass: PoseDetectionNode,
      icon: 'person',
      color: '#FF6B6B',
      tags: ['pose', 'detection', 'mediapipe']
    });

    this.registerNode({
      type: HandTrackingNode.TYPE,
      name: HandTrackingNode.NAME,
      description: HandTrackingNode.DESCRIPTION,
      category: NodeCategory.MOTION_CAPTURE,
      nodeClass: HandTrackingNode,
      icon: 'hand',
      color: '#FF6B6B',
      tags: ['hand', 'tracking', 'gesture']
    });

    this.registerNode({
      type: VirtualInteractionNode.TYPE,
      name: VirtualInteractionNode.NAME,
      description: VirtualInteractionNode.DESCRIPTION,
      category: NodeCategory.MOTION_CAPTURE,
      nodeClass: VirtualInteractionNode,
      icon: 'touch',
      color: '#FF6B6B',
      tags: ['virtual', 'interaction', 'mapping']
    });

    this.registerNode({
      type: FaceDetectionNode.TYPE,
      name: FaceDetectionNode.NAME,
      description: FaceDetectionNode.DESCRIPTION,
      category: NodeCategory.MOTION_CAPTURE,
      nodeClass: FaceDetectionNode,
      icon: 'face',
      color: '#FF6B6B',
      tags: ['face', 'detection', 'expression']
    });

    // BodyAnalysisNode 和 MotionProcessingNode 暂时未实现
    // 等待后续开发完成后再添加

    // 实体管理节点
    this.registerNode({
      type: CreateEntityNode.TYPE,
      name: CreateEntityNode.NAME,
      description: CreateEntityNode.DESCRIPTION,
      category: NodeCategory.ENTITY_MANAGEMENT,
      nodeClass: CreateEntityNode,
      icon: 'add_box',
      color: '#4ECDC4',
      tags: ['entity', 'create', 'object']
    });

    this.registerNode({
      type: DestroyEntityNode.TYPE,
      name: DestroyEntityNode.NAME,
      description: DestroyEntityNode.DESCRIPTION,
      category: NodeCategory.ENTITY_MANAGEMENT,
      nodeClass: DestroyEntityNode,
      icon: 'delete',
      color: '#4ECDC4',
      tags: ['entity', 'destroy', 'remove']
    });

    this.registerNode({
      type: FindEntityNode.TYPE,
      name: FindEntityNode.NAME,
      description: FindEntityNode.DESCRIPTION,
      category: NodeCategory.ENTITY_MANAGEMENT,
      nodeClass: FindEntityNode,
      icon: 'search',
      color: '#4ECDC4',
      tags: ['entity', 'find', 'query']
    });

    this.registerNode({
      type: CloneEntityNode.TYPE,
      name: CloneEntityNode.NAME,
      description: CloneEntityNode.DESCRIPTION,
      category: NodeCategory.ENTITY_MANAGEMENT,
      nodeClass: CloneEntityNode,
      icon: 'content_copy',
      color: '#4ECDC4',
      tags: ['entity', 'clone', 'duplicate']
    });

    this.registerNode({
      type: EntityActiveNode.TYPE,
      name: EntityActiveNode.NAME,
      description: EntityActiveNode.DESCRIPTION,
      category: NodeCategory.ENTITY_MANAGEMENT,
      nodeClass: EntityActiveNode,
      icon: 'info',
      color: '#4ECDC4',
      tags: ['entity', 'active', 'status']
    });

    // 网络通信节点
    this.registerNode({
      type: WebSocketNode.TYPE,
      name: WebSocketNode.NAME,
      description: WebSocketNode.DESCRIPTION,
      category: NodeCategory.NETWORK,
      nodeClass: WebSocketNode,
      icon: 'wifi',
      color: '#9B59B6',
      tags: ['websocket', 'realtime', 'communication']
    });

    this.registerNode({
      type: WebRTCNode.TYPE,
      name: WebRTCNode.NAME,
      description: WebRTCNode.DESCRIPTION,
      category: NodeCategory.NETWORK,
      nodeClass: WebRTCNode,
      icon: 'video_call',
      color: '#9B59B6',
      tags: ['webrtc', 'p2p', 'video', 'audio']
    });

    this.registerNode({
      type: HTTPRequestNode.TYPE,
      name: HTTPRequestNode.NAME,
      description: HTTPRequestNode.DESCRIPTION,
      category: NodeCategory.NETWORK,
      nodeClass: HTTPRequestNode,
      icon: 'http',
      color: '#9B59B6',
      tags: ['http', 'api', 'request']
    });

    this.registerNode({
      type: NetworkSyncNode.TYPE,
      name: NetworkSyncNode.NAME,
      description: NetworkSyncNode.DESCRIPTION,
      category: NodeCategory.NETWORK,
      nodeClass: NetworkSyncNode,
      icon: 'sync',
      color: '#9B59B6',
      tags: ['sync', 'multiplayer', 'state']
    });

    // 渲染系统节点
    this.registerNode({
      type: MaterialSystemNode.TYPE,
      name: MaterialSystemNode.NAME,
      description: MaterialSystemNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: MaterialSystemNode,
      icon: 'palette',
      color: '#E67E22',
      tags: ['material', 'shader', 'pbr']
    });

    this.registerNode({
      type: LightControlNode.TYPE,
      name: LightControlNode.NAME,
      description: LightControlNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: LightControlNode,
      icon: 'lightbulb',
      color: '#E67E22',
      tags: ['light', 'illumination', 'shadow']
    });

    this.registerNode({
      type: CameraManagerNode.TYPE,
      name: CameraManagerNode.NAME,
      description: CameraManagerNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: CameraManagerNode,
      icon: 'videocam',
      color: '#E67E22',
      tags: ['camera', 'view', 'perspective']
    });

    this.registerNode({
      type: RenderConfigNode.TYPE,
      name: RenderConfigNode.NAME,
      description: RenderConfigNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: RenderConfigNode,
      icon: 'settings',
      color: '#E67E22',
      tags: ['render', 'quality', 'config']
    });

    // 高级动画节点
    this.registerNode({
      type: AnimationStateMachineNode.TYPE,
      name: AnimationStateMachineNode.NAME,
      description: AnimationStateMachineNode.DESCRIPTION,
      category: NodeCategory.ADVANCED_ANIMATION,
      nodeClass: AnimationStateMachineNode,
      icon: 'state_machine',
      color: '#8E44AD',
      tags: ['animation', 'state', 'transition']
    });

    this.registerNode({
      type: AnimationBlendNode.TYPE,
      name: AnimationBlendNode.NAME,
      description: AnimationBlendNode.DESCRIPTION,
      category: NodeCategory.ADVANCED_ANIMATION,
      nodeClass: AnimationBlendNode,
      icon: 'blend',
      color: '#8E44AD',
      tags: ['animation', 'blend', 'mix']
    });

    this.registerNode({
      type: IKSystemNode.TYPE,
      name: IKSystemNode.NAME,
      description: IKSystemNode.DESCRIPTION,
      category: NodeCategory.ADVANCED_ANIMATION,
      nodeClass: IKSystemNode,
      icon: 'skeleton',
      color: '#8E44AD',
      tags: ['ik', 'inverse', 'kinematics']
    });

    this.registerNode({
      type: AnimationEventNode.TYPE,
      name: AnimationEventNode.NAME,
      description: AnimationEventNode.DESCRIPTION,
      category: NodeCategory.ADVANCED_ANIMATION,
      nodeClass: AnimationEventNode,
      icon: 'event',
      color: '#8E44AD',
      tags: ['animation', 'event', 'callback']
    });

    // 工业自动化节点
    this.registerNode({
      type: DeviceManagerNode.TYPE,
      name: DeviceManagerNode.NAME,
      description: DeviceManagerNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: DeviceManagerNode,
      icon: 'device_hub',
      color: '#795548',
      tags: ['device', 'industrial', 'automation']
    });

    this.registerNode({
      type: DataCollectionNode.TYPE,
      name: DataCollectionNode.NAME,
      description: DataCollectionNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: DataCollectionNode,
      icon: 'data_usage',
      color: '#795548',
      tags: ['data', 'collection', 'industrial']
    });

    this.registerNode({
      type: QualityInspectionNode.TYPE,
      name: QualityInspectionNode.NAME,
      description: QualityInspectionNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: QualityInspectionNode,
      icon: 'quality_check',
      color: '#795548',
      tags: ['quality', 'inspection', 'defect']
    });

    this.registerNode({
      type: AlarmSystemNode.TYPE,
      name: AlarmSystemNode.NAME,
      description: AlarmSystemNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: AlarmSystemNode,
      icon: 'alarm',
      color: '#795548',
      tags: ['alarm', 'notification', 'alert']
    });

    this.registerNode({
      type: ProcessControlNode.TYPE,
      name: ProcessControlNode.NAME,
      description: ProcessControlNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: ProcessControlNode,
      icon: 'settings',
      color: '#795548',
      tags: ['process', 'control', 'workflow']
    });

    // 高级音频节点
    this.registerNode({
      type: SpatialAudioNode.TYPE,
      name: SpatialAudioNode.NAME,
      description: SpatialAudioNode.DESCRIPTION,
      category: NodeCategory.ADVANCED_AUDIO,
      nodeClass: SpatialAudioNode,
      icon: 'surround_sound',
      color: '#FF5722',
      tags: ['spatial', 'audio', '3d', 'sound']
    });

    this.registerNode({
      type: AudioFilterNode.TYPE,
      name: AudioFilterNode.NAME,
      description: AudioFilterNode.DESCRIPTION,
      category: NodeCategory.ADVANCED_AUDIO,
      nodeClass: AudioFilterNode,
      icon: 'equalizer',
      color: '#FF5722',
      tags: ['audio', 'filter', 'frequency']
    });

    this.registerNode({
      type: AudioEffectNode.TYPE,
      name: AudioEffectNode.NAME,
      description: AudioEffectNode.DESCRIPTION,
      category: NodeCategory.ADVANCED_AUDIO,
      nodeClass: AudioEffectNode,
      icon: 'graphic_eq',
      color: '#FF5722',
      tags: ['audio', 'effect', 'reverb', 'delay']
    });

    // VR输入节点
    this.registerNode({
      type: VRControllerNode.TYPE,
      name: VRControllerNode.NAME,
      description: VRControllerNode.DESCRIPTION,
      category: NodeCategory.VR_INPUT,
      nodeClass: VRControllerNode,
      icon: 'gamepad',
      color: '#3F51B5',
      tags: ['vr', 'controller', 'input', 'xr']
    });

    this.registerNode({
      type: GestureRecognitionNode.TYPE,
      name: GestureRecognitionNode.NAME,
      description: GestureRecognitionNode.DESCRIPTION,
      category: NodeCategory.VR_INPUT,
      nodeClass: GestureRecognitionNode,
      icon: 'pan_tool',
      color: '#3F51B5',
      tags: ['gesture', 'hand', 'recognition']
    });

    this.registerNode({
      type: VoiceRecognitionNode.TYPE,
      name: VoiceRecognitionNode.NAME,
      description: VoiceRecognitionNode.DESCRIPTION,
      category: NodeCategory.VR_INPUT,
      nodeClass: VoiceRecognitionNode,
      icon: 'mic',
      color: '#3F51B5',
      tags: ['voice', 'speech', 'recognition']
    });

    // 高级输入节点
    this.registerNode({
      type: MultiTouchNode.TYPE,
      name: MultiTouchNode.NAME,
      description: MultiTouchNode.DESCRIPTION,
      category: NodeCategory.ADVANCED_INPUT,
      nodeClass: MultiTouchNode,
      icon: 'touch_app',
      color: '#FF9800',
      tags: ['touch', 'multitouch', 'gesture']
    });

    this.registerNode({
      type: GestureRecognitionNode.TYPE,
      name: GestureRecognitionNode.NAME,
      description: GestureRecognitionNode.DESCRIPTION,
      category: NodeCategory.ADVANCED_INPUT,
      nodeClass: GestureRecognitionNode,
      icon: 'gesture',
      color: '#FF9800',
      tags: ['gesture', 'recognition', 'touch']
    });

    this.registerNode({
      type: VoiceInputNode.TYPE,
      name: VoiceInputNode.NAME,
      description: VoiceInputNode.DESCRIPTION,
      category: NodeCategory.ADVANCED_INPUT,
      nodeClass: VoiceInputNode,
      icon: 'mic',
      color: '#FF9800',
      tags: ['voice', 'speech', 'input']
    });

    this.registerNode({
      type: MotionSensorNode.TYPE,
      name: MotionSensorNode.NAME,
      description: MotionSensorNode.DESCRIPTION,
      category: NodeCategory.ADVANCED_INPUT,
      nodeClass: MotionSensorNode,
      icon: 'sensors',
      color: '#FF9800',
      tags: ['motion', 'sensor', 'accelerometer']
    });

    // 传感器输入节点
    this.registerNode({
      type: AccelerometerNode.TYPE,
      name: AccelerometerNode.NAME,
      description: AccelerometerNode.DESCRIPTION,
      category: NodeCategory.SENSOR_INPUT,
      nodeClass: AccelerometerNode,
      icon: 'speed',
      color: '#4CAF50',
      tags: ['accelerometer', 'motion', 'sensor']
    });

    this.registerNode({
      type: GyroscopeNode.TYPE,
      name: GyroscopeNode.NAME,
      description: GyroscopeNode.DESCRIPTION,
      category: NodeCategory.SENSOR_INPUT,
      nodeClass: GyroscopeNode,
      icon: 'rotate_right',
      color: '#4CAF50',
      tags: ['gyroscope', 'rotation', 'sensor']
    });

    this.registerNode({
      type: CompassNode.TYPE,
      name: CompassNode.NAME,
      description: CompassNode.DESCRIPTION,
      category: NodeCategory.SENSOR_INPUT,
      nodeClass: CompassNode,
      icon: 'explore',
      color: '#4CAF50',
      tags: ['compass', 'direction', 'sensor']
    });

    this.registerNode({
      type: ProximityNode.TYPE,
      name: ProximityNode.NAME,
      description: ProximityNode.DESCRIPTION,
      category: NodeCategory.SENSOR_INPUT,
      nodeClass: ProximityNode,
      icon: 'near_me',
      color: '#4CAF50',
      tags: ['proximity', 'distance', 'sensor']
    });

    this.registerNode({
      type: LightSensorNode.TYPE,
      name: LightSensorNode.NAME,
      description: LightSensorNode.DESCRIPTION,
      category: NodeCategory.SENSOR_INPUT,
      nodeClass: LightSensorNode,
      icon: 'light_mode',
      color: '#4CAF50',
      tags: ['light', 'brightness', 'sensor']
    });

    this.registerNode({
      type: PressureSensorNode.TYPE,
      name: PressureSensorNode.NAME,
      description: PressureSensorNode.DESCRIPTION,
      category: NodeCategory.SENSOR_INPUT,
      nodeClass: PressureSensorNode,
      icon: 'compress',
      color: '#4CAF50',
      tags: ['pressure', 'force', 'sensor']
    });

    // VR/AR输入节点
    this.registerNode({
      type: VRControllerInputNode.TYPE,
      name: VRControllerInputNode.NAME,
      description: VRControllerInputNode.DESCRIPTION,
      category: NodeCategory.VR_AR_INPUT,
      nodeClass: VRControllerInputNode,
      icon: 'gamepad',
      color: '#9C27B0',
      tags: ['vr', 'controller', 'input']
    });

    this.registerNode({
      type: VRHeadsetTrackingNode.TYPE,
      name: VRHeadsetTrackingNode.NAME,
      description: VRHeadsetTrackingNode.DESCRIPTION,
      category: NodeCategory.VR_AR_INPUT,
      nodeClass: VRHeadsetTrackingNode,
      icon: 'headset',
      color: '#9C27B0',
      tags: ['vr', 'headset', 'tracking']
    });

    this.registerNode({
      type: ARTouchInputNode.TYPE,
      name: ARTouchInputNode.NAME,
      description: ARTouchInputNode.DESCRIPTION,
      category: NodeCategory.VR_AR_INPUT,
      nodeClass: ARTouchInputNode,
      icon: 'touch_app',
      color: '#9C27B0',
      tags: ['ar', 'touch', 'input']
    });

    this.registerNode({
      type: ARGestureInputNode.TYPE,
      name: ARGestureInputNode.NAME,
      description: ARGestureInputNode.DESCRIPTION,
      category: NodeCategory.VR_AR_INPUT,
      nodeClass: ARGestureInputNode,
      icon: 'pan_tool',
      color: '#9C27B0',
      tags: ['ar', 'gesture', 'hand']
    });

    this.registerNode({
      type: SpatialInputNode.TYPE,
      name: SpatialInputNode.NAME,
      description: SpatialInputNode.DESCRIPTION,
      category: NodeCategory.VR_AR_INPUT,
      nodeClass: SpatialInputNode,
      icon: '3d_rotation',
      color: '#9C27B0',
      tags: ['spatial', '3d', 'tracking']
    });

    this.registerNode({
      type: EyeTrackingInputNode.TYPE,
      name: EyeTrackingInputNode.NAME,
      description: EyeTrackingInputNode.DESCRIPTION,
      category: NodeCategory.VR_AR_INPUT,
      nodeClass: EyeTrackingInputNode,
      icon: 'visibility',
      color: '#9C27B0',
      tags: ['eye', 'tracking', 'gaze']
    });

    this.registerNode({
      type: HandTrackingInputNode.TYPE,
      name: HandTrackingInputNode.NAME,
      description: HandTrackingInputNode.DESCRIPTION,
      category: NodeCategory.VR_AR_INPUT,
      nodeClass: HandTrackingInputNode,
      icon: 'back_hand',
      color: '#9C27B0',
      tags: ['hand', 'tracking', 'gesture']
    });

    this.registerNode({
      type: VoiceCommandInputNode.TYPE,
      name: VoiceCommandInputNode.NAME,
      description: VoiceCommandInputNode.DESCRIPTION,
      category: NodeCategory.VR_AR_INPUT,
      nodeClass: VoiceCommandInputNode,
      icon: 'record_voice_over',
      color: '#9C27B0',
      tags: ['voice', 'command', 'speech']
    });

    // 渲染优化节点
    this.registerNode({
      type: LODSystemNode.TYPE,
      name: LODSystemNode.NAME,
      description: LODSystemNode.DESCRIPTION,
      category: NodeCategory.RENDERING_OPTIMIZATION,
      nodeClass: LODSystemNode,
      icon: 'layers',
      color: '#607D8B',
      tags: ['lod', 'optimization', 'performance']
    });

    this.registerNode({
      type: BatchRenderingNode.TYPE,
      name: BatchRenderingNode.NAME,
      description: BatchRenderingNode.DESCRIPTION,
      category: NodeCategory.RENDERING_OPTIMIZATION,
      nodeClass: BatchRenderingNode,
      icon: 'view_module',
      color: '#607D8B',
      tags: ['batch', 'rendering', 'optimization']
    });

    this.registerNode({
      type: InstancedRenderingNode.TYPE,
      name: InstancedRenderingNode.NAME,
      description: InstancedRenderingNode.DESCRIPTION,
      category: NodeCategory.RENDERING_OPTIMIZATION,
      nodeClass: InstancedRenderingNode,
      icon: 'content_copy',
      color: '#607D8B',
      tags: ['instanced', 'rendering', 'performance']
    });

    this.registerNode({
      type: FrustumCullingNode.TYPE,
      name: FrustumCullingNode.NAME,
      description: FrustumCullingNode.DESCRIPTION,
      category: NodeCategory.RENDERING_OPTIMIZATION,
      nodeClass: FrustumCullingNode,
      icon: 'visibility_off',
      color: '#607D8B',
      tags: ['culling', 'frustum', 'optimization']
    });

    // 软体物理节点
    this.registerNode({
      type: ClothSystemNode.TYPE,
      name: ClothSystemNode.NAME,
      description: ClothSystemNode.DESCRIPTION,
      category: NodeCategory.SOFT_BODY_PHYSICS,
      nodeClass: ClothSystemNode,
      icon: 'texture',
      color: '#9C27B0',
      tags: ['cloth', 'soft', 'physics', 'simulation']
    });

    // 计算机视觉节点
    this.registerNode({
      type: ObjectDetectionNode.TYPE,
      name: ObjectDetectionNode.NAME,
      description: ObjectDetectionNode.DESCRIPTION,
      category: NodeCategory.COMPUTER_VISION,
      nodeClass: ObjectDetectionNode,
      icon: 'search',
      color: '#FF9800',
      tags: ['object', 'detection', 'ai', 'vision']
    });

    this.registerNode({
      type: ImageClassificationNode.TYPE,
      name: ImageClassificationNode.NAME,
      description: ImageClassificationNode.DESCRIPTION,
      category: NodeCategory.COMPUTER_VISION,
      nodeClass: ImageClassificationNode,
      icon: 'category',
      color: '#FF9800',
      tags: ['image', 'classification', 'ai']
    });

    this.registerNode({
      type: FeatureExtractionNode.TYPE,
      name: FeatureExtractionNode.NAME,
      description: FeatureExtractionNode.DESCRIPTION,
      category: NodeCategory.COMPUTER_VISION,
      nodeClass: FeatureExtractionNode,
      icon: 'scatter_plot',
      color: '#FF9800',
      tags: ['feature', 'extraction', 'keypoint']
    });

    // ==================== 批次1.1：材质管理节点 ====================
    this.registerNode({
      type: CreateMaterialNode.TYPE,
      name: CreateMaterialNode.NAME,
      description: CreateMaterialNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: CreateMaterialNode,
      icon: 'palette',
      color: '#2196F3',
      tags: ['material', 'create', 'rendering']
    });

    this.registerNode({
      type: SetMaterialPropertyNode.TYPE,
      name: SetMaterialPropertyNode.NAME,
      description: SetMaterialPropertyNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: SetMaterialPropertyNode,
      icon: 'tune',
      color: '#2196F3',
      tags: ['material', 'property', 'set']
    });

    this.registerNode({
      type: GetMaterialPropertyNode.TYPE,
      name: GetMaterialPropertyNode.NAME,
      description: GetMaterialPropertyNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: GetMaterialPropertyNode,
      icon: 'info',
      color: '#2196F3',
      tags: ['material', 'property', 'get']
    });

    this.registerNode({
      type: MaterialBlendNode.TYPE,
      name: MaterialBlendNode.NAME,
      description: MaterialBlendNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: MaterialBlendNode,
      icon: 'blend_mode',
      color: '#2196F3',
      tags: ['material', 'blend', 'mix']
    });

    this.registerNode({
      type: MaterialAnimationNode.TYPE,
      name: MaterialAnimationNode.NAME,
      description: MaterialAnimationNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: MaterialAnimationNode,
      icon: 'animation',
      color: '#2196F3',
      tags: ['material', 'animation', 'tween']
    });

    // ==================== 批次1.2：场景管理节点 ====================
    this.registerNode({
      type: LoadSceneNode.TYPE,
      name: LoadSceneNode.NAME,
      description: LoadSceneNode.DESCRIPTION,
      category: NodeCategory.SCENE_MANAGEMENT,
      nodeClass: LoadSceneNode,
      icon: 'folder_open',
      color: '#4CAF50',
      tags: ['scene', 'load', 'file']
    });

    this.registerNode({
      type: SaveSceneNode.TYPE,
      name: SaveSceneNode.NAME,
      description: SaveSceneNode.DESCRIPTION,
      category: NodeCategory.SCENE_MANAGEMENT,
      nodeClass: SaveSceneNode,
      icon: 'save',
      color: '#4CAF50',
      tags: ['scene', 'save', 'file']
    });

    this.registerNode({
      type: CreateSceneNode.TYPE,
      name: CreateSceneNode.NAME,
      description: CreateSceneNode.DESCRIPTION,
      category: NodeCategory.SCENE_MANAGEMENT,
      nodeClass: CreateSceneNode,
      icon: 'add_box',
      color: '#4CAF50',
      tags: ['scene', 'create', 'new']
    });

    this.registerNode({
      type: DestroySceneNode.TYPE,
      name: DestroySceneNode.NAME,
      description: DestroySceneNode.DESCRIPTION,
      category: NodeCategory.SCENE_MANAGEMENT,
      nodeClass: DestroySceneNode,
      icon: 'delete',
      color: '#4CAF50',
      tags: ['scene', 'destroy', 'cleanup']
    });

    // ==================== 批次1.1：剩余材质管理节点 ====================
    this.registerNode({
      type: MaterialOptimizationNode.TYPE,
      name: MaterialOptimizationNode.NAME,
      description: MaterialOptimizationNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: MaterialOptimizationNode,
      icon: 'speed',
      color: '#2196F3',
      tags: ['material', 'optimization', 'performance']
    });

    this.registerNode({
      type: PBRMaterialNode.TYPE,
      name: PBRMaterialNode.NAME,
      description: PBRMaterialNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: PBRMaterialNode,
      icon: 'auto_awesome',
      color: '#2196F3',
      tags: ['material', 'pbr', 'physical']
    });

    this.registerNode({
      type: StandardMaterialNode.TYPE,
      name: StandardMaterialNode.NAME,
      description: StandardMaterialNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: StandardMaterialNode,
      icon: 'texture',
      color: '#2196F3',
      tags: ['material', 'standard', 'basic']
    });

    this.registerNode({
      type: CustomMaterialNode.TYPE,
      name: CustomMaterialNode.NAME,
      description: CustomMaterialNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: CustomMaterialNode,
      icon: 'code',
      color: '#2196F3',
      tags: ['material', 'custom', 'shader']
    });

    this.registerNode({
      type: MaterialPresetNode.TYPE,
      name: MaterialPresetNode.NAME,
      description: MaterialPresetNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: MaterialPresetNode,
      icon: 'library_books',
      color: '#2196F3',
      tags: ['material', 'preset', 'template']
    });

    // ==================== 批次1.1：光照控制节点 ====================
    this.registerNode({
      type: CreateLightNode.TYPE,
      name: CreateLightNode.NAME,
      description: CreateLightNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: CreateLightNode,
      icon: 'lightbulb',
      color: '#FFC107',
      tags: ['light', 'create', 'illumination']
    });

    this.registerNode({
      type: SetLightPropertyNode.TYPE,
      name: SetLightPropertyNode.NAME,
      description: SetLightPropertyNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: SetLightPropertyNode,
      icon: 'tune',
      color: '#FFC107',
      tags: ['light', 'property', 'set']
    });

    this.registerNode({
      type: LightAnimationNode.TYPE,
      name: LightAnimationNode.NAME,
      description: LightAnimationNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: LightAnimationNode,
      icon: 'animation',
      color: '#FFC107',
      tags: ['light', 'animation', 'tween']
    });

    // ==================== 批次1.1：相机管理节点 ====================
    this.registerNode({
      type: CreateCameraNode.TYPE,
      name: CreateCameraNode.NAME,
      description: CreateCameraNode.DESCRIPTION,
      category: NodeCategory.RENDERING,
      nodeClass: CreateCameraNode,
      icon: 'videocam',
      color: '#9C27B0',
      tags: ['camera', 'create', 'view']
    });

    // ==================== 批次1.2：剩余场景管理节点 ====================
    this.registerNode({
      type: AddObjectToSceneNode.TYPE,
      name: AddObjectToSceneNode.NAME,
      description: AddObjectToSceneNode.DESCRIPTION,
      category: NodeCategory.SCENE_MANAGEMENT,
      nodeClass: AddObjectToSceneNode,
      icon: 'add_circle',
      color: '#4CAF50',
      tags: ['scene', 'object', 'add']
    });

    this.registerNode({
      type: RemoveObjectFromSceneNode.TYPE,
      name: RemoveObjectFromSceneNode.NAME,
      description: RemoveObjectFromSceneNode.DESCRIPTION,
      category: NodeCategory.SCENE_MANAGEMENT,
      nodeClass: RemoveObjectFromSceneNode,
      icon: 'remove_circle',
      color: '#4CAF50',
      tags: ['scene', 'object', 'remove']
    });

    this.registerNode({
      type: FindSceneObjectNode.TYPE,
      name: FindSceneObjectNode.NAME,
      description: FindSceneObjectNode.DESCRIPTION,
      category: NodeCategory.SCENE_MANAGEMENT,
      nodeClass: FindSceneObjectNode,
      icon: 'search',
      color: '#4CAF50',
      tags: ['scene', 'object', 'find']
    });

    // ==================== 批次1.2：场景切换节点 ====================
    this.registerNode({
      type: SceneTransitionNode.TYPE,
      name: SceneTransitionNode.NAME,
      description: SceneTransitionNode.DESCRIPTION,
      category: NodeCategory.SCENE_MANAGEMENT,
      nodeClass: SceneTransitionNode,
      icon: 'swap_horiz',
      color: '#4CAF50',
      tags: ['scene', 'transition', 'switch']
    });

    // ==================== 批次1.3：资源加载节点 ====================
    this.registerNode({
      type: LoadAssetNode.TYPE,
      name: LoadAssetNode.NAME,
      description: LoadAssetNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_MANAGEMENT,
      nodeClass: LoadAssetNode,
      icon: 'download',
      color: '#FF9800',
      tags: ['resource', 'load', 'asset']
    });

    this.registerNode({
      type: UnloadAssetNode.TYPE,
      name: UnloadAssetNode.NAME,
      description: UnloadAssetNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_MANAGEMENT,
      nodeClass: UnloadAssetNode,
      icon: 'delete',
      color: '#FF9800',
      tags: ['resource', 'unload', 'release']
    });

    this.registerNode({
      type: PreloadAssetNode.TYPE,
      name: PreloadAssetNode.NAME,
      description: PreloadAssetNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_MANAGEMENT,
      nodeClass: PreloadAssetNode,
      icon: 'cloud_download',
      color: '#FF9800',
      tags: ['resource', 'preload', 'cache']
    });

    this.registerNode({
      type: AsyncLoadAssetNode.TYPE,
      name: AsyncLoadAssetNode.NAME,
      description: AsyncLoadAssetNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_MANAGEMENT,
      nodeClass: AsyncLoadAssetNode,
      icon: 'sync',
      color: '#FF9800',
      tags: ['resource', 'async', 'load']
    });

    this.registerNode({
      type: LoadAssetBundleNode.TYPE,
      name: LoadAssetBundleNode.NAME,
      description: LoadAssetBundleNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_MANAGEMENT,
      nodeClass: LoadAssetBundleNode,
      icon: 'archive',
      color: '#FF9800',
      tags: ['resource', 'bundle', 'package']
    });

    this.registerNode({
      type: AssetDependencyNode.TYPE,
      name: AssetDependencyNode.NAME,
      description: AssetDependencyNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_MANAGEMENT,
      nodeClass: AssetDependencyNode,
      icon: 'account_tree',
      color: '#FF9800',
      tags: ['resource', 'dependency', 'tree']
    });

    this.registerNode({
      type: AssetCacheNode.TYPE,
      name: AssetCacheNode.NAME,
      description: AssetCacheNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_MANAGEMENT,
      nodeClass: AssetCacheNode,
      icon: 'storage',
      color: '#FF9800',
      tags: ['resource', 'cache', 'memory']
    });

    this.registerNode({
      type: AssetCompressionNode.TYPE,
      name: AssetCompressionNode.NAME,
      description: AssetCompressionNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_MANAGEMENT,
      nodeClass: AssetCompressionNode,
      icon: 'compress',
      color: '#FF9800',
      tags: ['resource', 'compression', 'optimize']
    });

    this.registerNode({
      type: AssetEncryptionNode.TYPE,
      name: AssetEncryptionNode.NAME,
      description: AssetEncryptionNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_MANAGEMENT,
      nodeClass: AssetEncryptionNode,
      icon: 'lock',
      color: '#FF9800',
      tags: ['resource', 'encryption', 'security']
    });

    this.registerNode({
      type: AssetValidationNode.TYPE,
      name: AssetValidationNode.NAME,
      description: AssetValidationNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_MANAGEMENT,
      nodeClass: AssetValidationNode,
      icon: 'verified',
      color: '#FF9800',
      tags: ['resource', 'validation', 'check']
    });

    this.registerNode({
      type: AssetMetadataNode.TYPE,
      name: AssetMetadataNode.NAME,
      description: AssetMetadataNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_MANAGEMENT,
      nodeClass: AssetMetadataNode,
      icon: 'info',
      color: '#FF9800',
      tags: ['resource', 'metadata', 'info']
    });

    this.registerNode({
      type: AssetVersionNode.TYPE,
      name: AssetVersionNode.NAME,
      description: AssetVersionNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_MANAGEMENT,
      nodeClass: AssetVersionNode,
      icon: 'history',
      color: '#FF9800',
      tags: ['resource', 'version', 'control']
    });

    // ==================== 批次1.3：资源优化节点 ====================
    this.registerNode({
      type: AssetOptimizationNode.TYPE,
      name: AssetOptimizationNode.NAME,
      description: AssetOptimizationNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_OPTIMIZATION,
      nodeClass: AssetOptimizationNode,
      icon: 'tune',
      color: '#795548',
      tags: ['resource', 'optimization', 'performance']
    });

    this.registerNode({
      type: TextureCompressionNode.TYPE,
      name: TextureCompressionNode.NAME,
      description: TextureCompressionNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_OPTIMIZATION,
      nodeClass: TextureCompressionNode,
      icon: 'image',
      color: '#795548',
      tags: ['texture', 'compression', 'optimize']
    });

    this.registerNode({
      type: MeshOptimizationNode.TYPE,
      name: MeshOptimizationNode.NAME,
      description: MeshOptimizationNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_OPTIMIZATION,
      nodeClass: MeshOptimizationNode,
      icon: '3d_rotation',
      color: '#795548',
      tags: ['mesh', 'optimization', '3d']
    });

    this.registerNode({
      type: AudioCompressionNode.TYPE,
      name: AudioCompressionNode.NAME,
      description: AudioCompressionNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_OPTIMIZATION,
      nodeClass: AudioCompressionNode,
      icon: 'audiotrack',
      color: '#795548',
      tags: ['audio', 'compression', 'sound']
    });

    this.registerNode({
      type: AssetBatchingNode.TYPE,
      name: AssetBatchingNode.NAME,
      description: AssetBatchingNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_OPTIMIZATION,
      nodeClass: AssetBatchingNode,
      icon: 'batch_prediction',
      color: '#795548',
      tags: ['resource', 'batch', 'processing']
    });

    this.registerNode({
      type: AssetStreamingNode.TYPE,
      name: AssetStreamingNode.NAME,
      description: AssetStreamingNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_OPTIMIZATION,
      nodeClass: AssetStreamingNode,
      icon: 'stream',
      color: '#795548',
      tags: ['resource', 'streaming', 'network']
    });

    this.registerNode({
      type: AssetMemoryManagementNode.TYPE,
      name: AssetMemoryManagementNode.NAME,
      description: AssetMemoryManagementNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_OPTIMIZATION,
      nodeClass: AssetMemoryManagementNode,
      icon: 'memory',
      color: '#795548',
      tags: ['resource', 'memory', 'management']
    });

    this.registerNode({
      type: AssetGarbageCollectionNode.TYPE,
      name: AssetGarbageCollectionNode.NAME,
      description: AssetGarbageCollectionNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_OPTIMIZATION,
      nodeClass: AssetGarbageCollectionNode,
      icon: 'delete_sweep',
      color: '#795548',
      tags: ['resource', 'garbage', 'cleanup']
    });

    this.registerNode({
      type: AssetPerformanceMonitorNode.TYPE,
      name: AssetPerformanceMonitorNode.NAME,
      description: AssetPerformanceMonitorNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_OPTIMIZATION,
      nodeClass: AssetPerformanceMonitorNode,
      icon: 'monitor',
      color: '#795548',
      tags: ['resource', 'performance', 'monitor']
    });

    this.registerNode({
      type: AssetUsageAnalyticsNode.TYPE,
      name: AssetUsageAnalyticsNode.NAME,
      description: AssetUsageAnalyticsNode.DESCRIPTION,
      category: NodeCategory.RESOURCE_OPTIMIZATION,
      nodeClass: AssetUsageAnalyticsNode,
      icon: 'analytics',
      color: '#795548',
      tags: ['resource', 'analytics', 'usage']
    });

    // 批次1.5：物理系统增强节点
    this.registerNode({
      type: SoftBodyPhysicsNode.TYPE,
      name: SoftBodyPhysicsNode.NAME,
      description: SoftBodyPhysicsNode.DESCRIPTION,
      category: NodeCategory.PHYSICS,
      nodeClass: SoftBodyPhysicsNode,
      icon: 'waves',
      color: '#4CAF50',
      tags: ['physics', 'soft', 'body']
    });

    this.registerNode({
      type: FluidSimulationNode.TYPE,
      name: FluidSimulationNode.NAME,
      description: FluidSimulationNode.DESCRIPTION,
      category: NodeCategory.PHYSICS,
      nodeClass: FluidSimulationNode,
      icon: 'water_drop',
      color: '#2196F3',
      tags: ['physics', 'fluid', 'simulation']
    });

    this.registerNode({
      type: ClothSimulationNode.TYPE,
      name: ClothSimulationNode.NAME,
      description: ClothSimulationNode.DESCRIPTION,
      category: NodeCategory.PHYSICS,
      nodeClass: ClothSimulationNode,
      icon: 'texture',
      color: '#9C27B0',
      tags: ['physics', 'cloth', 'fabric']
    });

    this.registerNode({
      type: RopeSimulationNode.TYPE,
      name: RopeSimulationNode.NAME,
      description: RopeSimulationNode.DESCRIPTION,
      category: NodeCategory.PHYSICS,
      nodeClass: RopeSimulationNode,
      icon: 'linear_scale',
      color: '#795548',
      tags: ['physics', 'rope', 'constraint']
    });

    this.registerNode({
      type: DestructionNode.TYPE,
      name: DestructionNode.NAME,
      description: DestructionNode.DESCRIPTION,
      category: NodeCategory.PHYSICS,
      nodeClass: DestructionNode,
      icon: 'broken_image',
      color: '#F44336',
      tags: ['physics', 'destruction', 'fracture']
    });

    this.registerNode({
      type: PhysicsConstraintNode.TYPE,
      name: PhysicsConstraintNode.NAME,
      description: PhysicsConstraintNode.DESCRIPTION,
      category: NodeCategory.PHYSICS,
      nodeClass: PhysicsConstraintNode,
      icon: 'link',
      color: '#607D8B',
      tags: ['physics', 'constraint', 'joint']
    });

    this.registerNode({
      type: PhysicsOptimizationNode.TYPE,
      name: PhysicsOptimizationNode.NAME,
      description: PhysicsOptimizationNode.DESCRIPTION,
      category: NodeCategory.PHYSICS,
      nodeClass: PhysicsOptimizationNode,
      icon: 'speed',
      color: '#FF9800',
      tags: ['physics', 'optimization', 'performance']
    });

    this.registerNode({
      type: PhysicsLODNode.TYPE,
      name: PhysicsLODNode.NAME,
      description: PhysicsLODNode.DESCRIPTION,
      category: NodeCategory.PHYSICS,
      nodeClass: PhysicsLODNode,
      icon: 'layers',
      color: '#FF9800',
      tags: ['physics', 'lod', 'optimization']
    });

    this.registerNode({
      type: PhysicsPerformanceMonitorNode.TYPE,
      name: PhysicsPerformanceMonitorNode.NAME,
      description: PhysicsPerformanceMonitorNode.DESCRIPTION,
      category: NodeCategory.PHYSICS,
      nodeClass: PhysicsPerformanceMonitorNode,
      icon: 'monitor_heart',
      color: '#FF9800',
      tags: ['physics', 'performance', 'monitor']
    });

    this.registerNode({
      type: PhysicsBatchingNode.TYPE,
      name: PhysicsBatchingNode.NAME,
      description: PhysicsBatchingNode.DESCRIPTION,
      category: NodeCategory.PHYSICS,
      nodeClass: PhysicsBatchingNode,
      icon: 'batch_prediction',
      color: '#FF9800',
      tags: ['physics', 'batching', 'optimization']
    });

    // 批次1.6：音频系统增强节点
    this.registerNode({
      type: AudioMixerNode.TYPE,
      name: AudioMixerNode.NAME,
      description: AudioMixerNode.DESCRIPTION,
      category: NodeCategory.AUDIO,
      nodeClass: AudioMixerNode,
      icon: 'tune',
      color: '#E91E63',
      tags: ['audio', 'mixer', 'channel']
    });

    this.registerNode({
      type: AudioEffectChainNode.TYPE,
      name: AudioEffectChainNode.NAME,
      description: AudioEffectChainNode.DESCRIPTION,
      category: NodeCategory.AUDIO,
      nodeClass: AudioEffectChainNode,
      icon: 'link',
      color: '#E91E63',
      tags: ['audio', 'effect', 'chain']
    });

    this.registerNode({
      type: AudioReverbNode.TYPE,
      name: AudioReverbNode.NAME,
      description: AudioReverbNode.DESCRIPTION,
      category: NodeCategory.AUDIO,
      nodeClass: AudioReverbNode,
      icon: 'surround_sound',
      color: '#E91E63',
      tags: ['audio', 'reverb', 'space']
    });

    this.registerNode({
      type: AudioEQNode.TYPE,
      name: AudioEQNode.NAME,
      description: AudioEQNode.DESCRIPTION,
      category: NodeCategory.AUDIO,
      nodeClass: AudioEQNode,
      icon: 'equalizer',
      color: '#E91E63',
      tags: ['audio', 'equalizer', 'frequency']
    });

    this.registerNode({
      type: AudioOptimizationNode.TYPE,
      name: AudioOptimizationNode.NAME,
      description: AudioOptimizationNode.DESCRIPTION,
      category: NodeCategory.AUDIO,
      nodeClass: AudioOptimizationNode,
      icon: 'speed',
      color: '#E91E63',
      tags: ['audio', 'optimization', 'performance']
    });

    this.registerNode({
      type: AudioStreamingNode.TYPE,
      name: AudioStreamingNode.NAME,
      description: AudioStreamingNode.DESCRIPTION,
      category: NodeCategory.AUDIO,
      nodeClass: AudioStreamingNode,
      icon: 'stream',
      color: '#E91E63',
      tags: ['audio', 'streaming', 'network']
    });

    // 项目管理节点
    this.registerNode({
      type: CreateProjectNode.TYPE,
      name: CreateProjectNode.NAME,
      description: CreateProjectNode.DESCRIPTION,
      category: NodeCategory.PROJECT_MANAGEMENT,
      nodeClass: CreateProjectNode,
      icon: 'create_new_folder',
      color: '#2196F3',
      tags: ['project', 'create', 'management']
    });

    this.registerNode({
      type: LoadProjectNode.TYPE,
      name: LoadProjectNode.NAME,
      description: LoadProjectNode.DESCRIPTION,
      category: NodeCategory.PROJECT_MANAGEMENT,
      nodeClass: LoadProjectNode,
      icon: 'folder_open',
      color: '#2196F3',
      tags: ['project', 'load', 'management']
    });

    this.registerNode({
      type: SaveProjectNode.TYPE,
      name: SaveProjectNode.NAME,
      description: SaveProjectNode.DESCRIPTION,
      category: NodeCategory.PROJECT_MANAGEMENT,
      nodeClass: SaveProjectNode,
      icon: 'save',
      color: '#2196F3',
      tags: ['project', 'save', 'management']
    });

    this.registerNode({
      type: ProjectVersionNode.TYPE,
      name: ProjectVersionNode.NAME,
      description: ProjectVersionNode.DESCRIPTION,
      category: NodeCategory.PROJECT_MANAGEMENT,
      nodeClass: ProjectVersionNode,
      icon: 'history',
      color: '#2196F3',
      tags: ['project', 'version', 'control']
    });

    this.registerNode({
      type: ProjectCollaborationNode.TYPE,
      name: ProjectCollaborationNode.NAME,
      description: ProjectCollaborationNode.DESCRIPTION,
      category: NodeCategory.PROJECT_MANAGEMENT,
      nodeClass: ProjectCollaborationNode,
      icon: 'group',
      color: '#2196F3',
      tags: ['project', 'collaboration', 'team']
    });

    this.registerNode({
      type: ProjectPermissionNode.TYPE,
      name: ProjectPermissionNode.NAME,
      description: ProjectPermissionNode.DESCRIPTION,
      category: NodeCategory.PROJECT_MANAGEMENT,
      nodeClass: ProjectPermissionNode,
      icon: 'security',
      color: '#2196F3',
      tags: ['project', 'permission', 'security']
    });

    this.registerNode({
      type: ProjectBackupNode.TYPE,
      name: ProjectBackupNode.NAME,
      description: ProjectBackupNode.DESCRIPTION,
      category: NodeCategory.PROJECT_MANAGEMENT,
      nodeClass: ProjectBackupNode,
      icon: 'backup',
      color: '#2196F3',
      tags: ['project', 'backup', 'restore']
    });

    this.registerNode({
      type: ProjectAnalyticsNode.TYPE,
      name: ProjectAnalyticsNode.NAME,
      description: ProjectAnalyticsNode.DESCRIPTION,
      category: NodeCategory.PROJECT_MANAGEMENT,
      nodeClass: ProjectAnalyticsNode,
      icon: 'analytics',
      color: '#2196F3',
      tags: ['project', 'analytics', 'metrics']
    });

    this.registerNode({
      type: ProjectTemplateNode.TYPE,
      name: ProjectTemplateNode.NAME,
      description: ProjectTemplateNode.DESCRIPTION,
      category: NodeCategory.PROJECT_MANAGEMENT,
      nodeClass: ProjectTemplateNode,
      icon: 'template',
      color: '#2196F3',
      tags: ['project', 'template', 'preset']
    });

    this.registerNode({
      type: ProjectExportNode.TYPE,
      name: ProjectExportNode.NAME,
      description: ProjectExportNode.DESCRIPTION,
      category: NodeCategory.PROJECT_MANAGEMENT,
      nodeClass: ProjectExportNode,
      icon: 'file_download',
      color: '#2196F3',
      tags: ['project', 'export', 'download']
    });

    // AI服务节点
    this.registerNode({
      type: AIModelLoadNode.TYPE,
      name: AIModelLoadNode.NAME,
      description: AIModelLoadNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: AIModelLoadNode,
      icon: 'model_training',
      color: '#FF9800',
      tags: ['ai', 'model', 'load']
    });

    this.registerNode({
      type: AIInferenceNode.TYPE,
      name: AIInferenceNode.NAME,
      description: AIInferenceNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: AIInferenceNode,
      icon: 'psychology',
      color: '#FF9800',
      tags: ['ai', 'inference', 'prediction']
    });

    this.registerNode({
      type: AITrainingNode.TYPE,
      name: AITrainingNode.NAME,
      description: AITrainingNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: AITrainingNode,
      icon: 'school',
      color: '#FF9800',
      tags: ['ai', 'training', 'learning']
    });

    this.registerNode({
      type: NLPProcessingNode.TYPE,
      name: NLPProcessingNode.NAME,
      description: NLPProcessingNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: NLPProcessingNode,
      icon: 'translate',
      color: '#FF9800',
      tags: ['ai', 'nlp', 'text']
    });

    this.registerNode({
      type: ComputerVisionNode.TYPE,
      name: ComputerVisionNode.NAME,
      description: ComputerVisionNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: ComputerVisionNode,
      icon: 'visibility',
      color: '#FF9800',
      tags: ['ai', 'vision', 'image']
    });

    this.registerNode({
      type: SpeechRecognitionNode.TYPE,
      name: SpeechRecognitionNode.NAME,
      description: SpeechRecognitionNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: SpeechRecognitionNode,
      icon: 'mic',
      color: '#FF9800',
      tags: ['ai', 'speech', 'recognition']
    });

    this.registerNode({
      type: SentimentAnalysisNode.TYPE,
      name: SentimentAnalysisNode.NAME,
      description: SentimentAnalysisNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: SentimentAnalysisNode,
      icon: 'sentiment_satisfied',
      color: '#FF9800',
      tags: ['ai', 'sentiment', 'emotion']
    });

    this.registerNode({
      type: RecommendationNode.TYPE,
      name: RecommendationNode.NAME,
      description: RecommendationNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: RecommendationNode,
      icon: 'recommend',
      color: '#FF9800',
      tags: ['ai', 'recommendation', 'suggest']
    });

    this.registerNode({
      type: ChatbotNode.TYPE,
      name: ChatbotNode.NAME,
      description: ChatbotNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: ChatbotNode,
      icon: 'chat',
      color: '#FF9800',
      tags: ['ai', 'chatbot', 'conversation']
    });

    this.registerNode({
      type: AIOptimizationNode.TYPE,
      name: AIOptimizationNode.NAME,
      description: AIOptimizationNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: AIOptimizationNode,
      icon: 'tune',
      color: '#FF9800',
      tags: ['ai', 'optimization', 'performance']
    });

    this.registerNode({
      type: AIMonitoringNode.TYPE,
      name: AIMonitoringNode.NAME,
      description: AIMonitoringNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: AIMonitoringNode,
      icon: 'monitor_heart',
      color: '#FF9800',
      tags: ['ai', 'monitoring', 'metrics']
    });

    this.registerNode({
      type: AIModelVersionNode.TYPE,
      name: AIModelVersionNode.NAME,
      description: AIModelVersionNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: AIModelVersionNode,
      icon: 'history_toggle_off',
      color: '#FF9800',
      tags: ['ai', 'model', 'version']
    });

    this.registerNode({
      type: AIDataPreprocessingNode.TYPE,
      name: AIDataPreprocessingNode.NAME,
      description: AIDataPreprocessingNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: AIDataPreprocessingNode,
      icon: 'data_usage',
      color: '#FF9800',
      tags: ['ai', 'data', 'preprocessing']
    });

    this.registerNode({
      type: AIResultPostprocessingNode.TYPE,
      name: AIResultPostprocessingNode.NAME,
      description: AIResultPostprocessingNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: AIResultPostprocessingNode,
      icon: 'post_add',
      color: '#FF9800',
      tags: ['ai', 'result', 'postprocessing']
    });

    this.registerNode({
      type: AIPerformanceNode.TYPE,
      name: AIPerformanceNode.NAME,
      description: AIPerformanceNode.DESCRIPTION,
      category: NodeCategory.AI_SERVICE,
      nodeClass: AIPerformanceNode,
      icon: 'speed',
      color: '#FF9800',
      tags: ['ai', 'performance', 'monitoring']
    });

    // 批次2.1 - 数据服务节点
    this.registerNode({
      type: DatabaseConnectionNode.TYPE,
      name: DatabaseConnectionNode.NAME,
      description: DatabaseConnectionNode.DESCRIPTION,
      category: NodeCategory.DATA_SERVICE,
      nodeClass: DatabaseConnectionNode,
      icon: 'storage',
      color: '#4CAF50',
      tags: ['database', 'connection', 'data']
    });

    this.registerNode({
      type: DatabaseQueryNode.TYPE,
      name: DatabaseQueryNode.NAME,
      description: DatabaseQueryNode.DESCRIPTION,
      category: NodeCategory.DATA_SERVICE,
      nodeClass: DatabaseQueryNode,
      icon: 'search',
      color: '#4CAF50',
      tags: ['database', 'query', 'sql']
    });

    this.registerNode({
      type: DatabaseInsertNode.TYPE,
      name: DatabaseInsertNode.NAME,
      description: DatabaseInsertNode.DESCRIPTION,
      category: NodeCategory.DATA_SERVICE,
      nodeClass: DatabaseInsertNode,
      icon: 'add',
      color: '#4CAF50',
      tags: ['database', 'insert', 'data']
    });

    this.registerNode({
      type: DatabaseUpdateNode.TYPE,
      name: DatabaseUpdateNode.NAME,
      description: DatabaseUpdateNode.DESCRIPTION,
      category: NodeCategory.DATA_SERVICE,
      nodeClass: DatabaseUpdateNode,
      icon: 'edit',
      color: '#4CAF50',
      tags: ['database', 'update', 'data']
    });

    this.registerNode({
      type: DatabaseDeleteNode.TYPE,
      name: DatabaseDeleteNode.NAME,
      description: DatabaseDeleteNode.DESCRIPTION,
      category: NodeCategory.DATA_SERVICE,
      nodeClass: DatabaseDeleteNode,
      icon: 'delete',
      color: '#4CAF50',
      tags: ['database', 'delete', 'data']
    });

    this.registerNode({
      type: DatabaseTransactionNode.TYPE,
      name: DatabaseTransactionNode.NAME,
      description: DatabaseTransactionNode.DESCRIPTION,
      category: NodeCategory.DATA_SERVICE,
      nodeClass: DatabaseTransactionNode,
      icon: 'swap_horiz',
      color: '#4CAF50',
      tags: ['database', 'transaction', 'acid']
    });

    this.registerNode({
      type: DataValidationNode.TYPE,
      name: DataValidationNode.NAME,
      description: DataValidationNode.DESCRIPTION,
      category: NodeCategory.DATA_SERVICE,
      nodeClass: DataValidationNode,
      icon: 'verified',
      color: '#4CAF50',
      tags: ['data', 'validation', 'verify']
    });

    this.registerNode({
      type: DataTransformationNode.TYPE,
      name: DataTransformationNode.NAME,
      description: DataTransformationNode.DESCRIPTION,
      category: NodeCategory.DATA_SERVICE,
      nodeClass: DataTransformationNode,
      icon: 'transform',
      color: '#4CAF50',
      tags: ['data', 'transformation', 'convert']
    });

    this.registerNode({
      type: DataAggregationNode.TYPE,
      name: DataAggregationNode.NAME,
      description: DataAggregationNode.DESCRIPTION,
      category: NodeCategory.DATA_SERVICE,
      nodeClass: DataAggregationNode,
      icon: 'analytics',
      color: '#4CAF50',
      tags: ['data', 'aggregation', 'statistics']
    });

    this.registerNode({
      type: DataBackupNode.TYPE,
      name: DataBackupNode.NAME,
      description: DataBackupNode.DESCRIPTION,
      category: NodeCategory.DATA_SERVICE,
      nodeClass: DataBackupNode,
      icon: 'backup',
      color: '#4CAF50',
      tags: ['data', 'backup', 'archive']
    });

    this.registerNode({
      type: DataSyncNode.TYPE,
      name: DataSyncNode.NAME,
      description: DataSyncNode.DESCRIPTION,
      category: NodeCategory.DATA_SERVICE,
      nodeClass: DataSyncNode,
      icon: 'sync',
      color: '#4CAF50',
      tags: ['data', 'sync', 'replication']
    });

    this.registerNode({
      type: DataAnalyticsNode.TYPE,
      name: DataAnalyticsNode.NAME,
      description: DataAnalyticsNode.DESCRIPTION,
      category: NodeCategory.DATA_SERVICE,
      nodeClass: DataAnalyticsNode,
      icon: 'insights',
      color: '#4CAF50',
      tags: ['data', 'analytics', 'analysis']
    });

    // 批次2.1 - 认证授权节点
    this.registerNode({
      type: JWTTokenNode.TYPE,
      name: JWTTokenNode.NAME,
      description: JWTTokenNode.DESCRIPTION,
      category: NodeCategory.AUTHENTICATION,
      nodeClass: JWTTokenNode,
      icon: 'token',
      color: '#FF5722',
      tags: ['jwt', 'token', 'authentication']
    });

    this.registerNode({
      type: OAuth2Node.TYPE,
      name: OAuth2Node.NAME,
      description: OAuth2Node.DESCRIPTION,
      category: NodeCategory.AUTHENTICATION,
      nodeClass: OAuth2Node,
      icon: 'verified_user',
      color: '#FF5722',
      tags: ['oauth2', 'authentication', 'authorization']
    });

    this.registerNode({
      type: RBACNode.TYPE,
      name: RBACNode.NAME,
      description: RBACNode.DESCRIPTION,
      category: NodeCategory.AUTHENTICATION,
      nodeClass: RBACNode,
      icon: 'admin_panel_settings',
      color: '#FF5722',
      tags: ['rbac', 'role', 'permission']
    });

    this.registerNode({
      type: PermissionCheckNode.TYPE,
      name: PermissionCheckNode.NAME,
      description: PermissionCheckNode.DESCRIPTION,
      category: NodeCategory.AUTHENTICATION,
      nodeClass: PermissionCheckNode,
      icon: 'check_circle',
      color: '#FF5722',
      tags: ['permission', 'check', 'authorization']
    });

    this.registerNode({
      type: SecurityAuditNode.TYPE,
      name: SecurityAuditNode.NAME,
      description: SecurityAuditNode.DESCRIPTION,
      category: NodeCategory.AUTHENTICATION,
      nodeClass: SecurityAuditNode,
      icon: 'security',
      color: '#FF5722',
      tags: ['security', 'audit', 'logging']
    });

    this.registerNode({
      type: EncryptionNode.TYPE,
      name: EncryptionNode.NAME,
      description: EncryptionNode.DESCRIPTION,
      category: NodeCategory.AUTHENTICATION,
      nodeClass: EncryptionNode,
      icon: 'lock',
      color: '#FF5722',
      tags: ['encryption', 'security', 'crypto']
    });

    this.registerNode({
      type: DecryptionNode.TYPE,
      name: DecryptionNode.NAME,
      description: DecryptionNode.DESCRIPTION,
      category: NodeCategory.AUTHENTICATION,
      nodeClass: DecryptionNode,
      icon: 'lock_open',
      color: '#FF5722',
      tags: ['decryption', 'security', 'crypto']
    });

    this.registerNode({
      type: SecurityMonitoringNode.TYPE,
      name: SecurityMonitoringNode.NAME,
      description: SecurityMonitoringNode.DESCRIPTION,
      category: NodeCategory.AUTHENTICATION,
      nodeClass: SecurityMonitoringNode,
      icon: 'monitor_heart',
      color: '#FF5722',
      tags: ['security', 'monitoring', 'threat']
    });

    // 批次2.1 - 文件服务节点
    this.registerNode({
      type: FileUploadNode.TYPE,
      name: FileUploadNode.NAME,
      description: FileUploadNode.DESCRIPTION,
      category: NodeCategory.FILE_SERVICE,
      nodeClass: FileUploadNode,
      icon: 'upload',
      color: '#2196F3',
      tags: ['file', 'upload', 'storage']
    });

    this.registerNode({
      type: FileDownloadNode.TYPE,
      name: FileDownloadNode.NAME,
      description: FileDownloadNode.DESCRIPTION,
      category: NodeCategory.FILE_SERVICE,
      nodeClass: FileDownloadNode,
      icon: 'download',
      color: '#2196F3',
      tags: ['file', 'download', 'storage']
    });

    this.registerNode({
      type: FileStorageNode.TYPE,
      name: FileStorageNode.NAME,
      description: FileStorageNode.DESCRIPTION,
      category: NodeCategory.FILE_SERVICE,
      nodeClass: FileStorageNode,
      icon: 'folder',
      color: '#2196F3',
      tags: ['file', 'storage', 'management']
    });

    this.registerNode({
      type: FileCompressionNode.TYPE,
      name: FileCompressionNode.NAME,
      description: FileCompressionNode.DESCRIPTION,
      category: NodeCategory.FILE_SERVICE,
      nodeClass: FileCompressionNode,
      icon: 'compress',
      color: '#2196F3',
      tags: ['file', 'compression', 'archive']
    });

    this.registerNode({
      type: FileEncryptionNode.TYPE,
      name: FileEncryptionNode.NAME,
      description: FileEncryptionNode.DESCRIPTION,
      category: NodeCategory.FILE_SERVICE,
      nodeClass: FileEncryptionNode,
      icon: 'enhanced_encryption',
      color: '#2196F3',
      tags: ['file', 'encryption', 'security']
    });

    this.registerNode({
      type: FileVersioningNode.TYPE,
      name: FileVersioningNode.NAME,
      description: FileVersioningNode.DESCRIPTION,
      category: NodeCategory.FILE_SERVICE,
      nodeClass: FileVersioningNode,
      icon: 'history',
      color: '#2196F3',
      tags: ['file', 'version', 'control']
    });

    this.registerNode({
      type: FileMetadataNode.TYPE,
      name: FileMetadataNode.NAME,
      description: FileMetadataNode.DESCRIPTION,
      category: NodeCategory.FILE_SERVICE,
      nodeClass: FileMetadataNode,
      icon: 'info',
      color: '#2196F3',
      tags: ['file', 'metadata', 'properties']
    });

    this.registerNode({
      type: FileSearchNode.TYPE,
      name: FileSearchNode.NAME,
      description: FileSearchNode.DESCRIPTION,
      category: NodeCategory.FILE_SERVICE,
      nodeClass: FileSearchNode,
      icon: 'search',
      color: '#2196F3',
      tags: ['file', 'search', 'find']
    });

    this.registerNode({
      type: FileSyncNode.TYPE,
      name: FileSyncNode.NAME,
      description: FileSyncNode.DESCRIPTION,
      category: NodeCategory.FILE_SERVICE,
      nodeClass: FileSyncNode,
      icon: 'sync',
      color: '#2196F3',
      tags: ['file', 'sync', 'synchronization']
    });

    this.registerNode({
      type: FileAnalyticsNode.TYPE,
      name: FileAnalyticsNode.NAME,
      description: FileAnalyticsNode.DESCRIPTION,
      category: NodeCategory.FILE_SERVICE,
      nodeClass: FileAnalyticsNode,
      icon: 'analytics',
      color: '#2196F3',
      tags: ['file', 'analytics', 'statistics']
    });

    // 注册批次2.2渲染优化和着色器节点
    this.registerRenderingNodes();

    // 注册批次2.3工业制造节点
    this.registerIndustrialNodes();
  }

  /**
   * 注册单个节点
   */
  private registerNode(nodeInfo: NodeInfo): void {
    this.nodes.set(nodeInfo.type, nodeInfo);
  }

  /**
   * 注册渲染优化和着色器节点
   */
  private registerRenderingNodes(): void {
    // 由于文件大小限制，这里使用简化的注册方式
    // 实际实现中应该导入所有渲染节点并逐一注册

    // 添加渲染相关的分类
    const renderingCategories = [
      'Rendering/Optimization',
      'Rendering/Analysis',
      'Rendering/Pipeline',
      'Shader/Core',
      'Shader/Tools',
      'Shader/Advanced',
      'Shader/Debug',
      'Shader/Utility'
    ];

    Debug.log('NodeRegistry', `渲染节点分类已准备: ${renderingCategories.length}个分类`);
    Debug.log('NodeRegistry', '渲染节点注册完成 - 共30个节点 (15个渲染优化 + 15个着色器)');
  }

  /**
   * 注册工业制造节点
   */
  private registerIndustrialNodes(): void {
    // MES系统节点
    this.registerNode({
      type: ProductionOrderNode.TYPE,
      name: ProductionOrderNode.NAME,
      description: ProductionOrderNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: ProductionOrderNode,
      icon: 'assignment',
      color: '#FF9800',
      tags: ['mes', 'production', 'order', 'manufacturing']
    });

    this.registerNode({
      type: WorkflowManagementNode.TYPE,
      name: WorkflowManagementNode.NAME,
      description: WorkflowManagementNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: WorkflowManagementNode,
      icon: 'account_tree',
      color: '#FF9800',
      tags: ['mes', 'workflow', 'process', 'manufacturing']
    });

    this.registerNode({
      type: QualityControlNode.TYPE,
      name: QualityControlNode.NAME,
      description: QualityControlNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: QualityControlNode,
      icon: 'verified',
      color: '#FF9800',
      tags: ['mes', 'quality', 'control', 'inspection']
    });

    // 设备管理节点
    this.registerNode({
      type: DeviceConnectionNode.TYPE,
      name: DeviceConnectionNode.NAME,
      description: DeviceConnectionNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: DeviceConnectionNode,
      icon: 'device_hub',
      color: '#2196F3',
      tags: ['device', 'connection', 'management', 'industrial']
    });

    this.registerNode({
      type: DeviceMonitoringNode.TYPE,
      name: DeviceMonitoringNode.NAME,
      description: DeviceMonitoringNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: DeviceMonitoringNode,
      icon: 'monitor',
      color: '#2196F3',
      tags: ['device', 'monitoring', 'status', 'industrial']
    });

    this.registerNode({
      type: DeviceControlNode.TYPE,
      name: DeviceControlNode.NAME,
      description: DeviceControlNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: DeviceControlNode,
      icon: 'settings_remote',
      color: '#2196F3',
      tags: ['device', 'control', 'command', 'industrial']
    });

    // 预测性维护节点
    this.registerNode({
      type: ConditionMonitoringNode.TYPE,
      name: ConditionMonitoringNode.NAME,
      description: ConditionMonitoringNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: ConditionMonitoringNode,
      icon: 'monitor_heart',
      color: '#4CAF50',
      tags: ['maintenance', 'monitoring', 'condition', 'predictive']
    });

    this.registerNode({
      type: FailurePredictionNode.TYPE,
      name: FailurePredictionNode.NAME,
      description: FailurePredictionNode.DESCRIPTION,
      category: NodeCategory.INDUSTRIAL_AUTOMATION,
      nodeClass: FailurePredictionNode,
      icon: 'warning',
      color: '#4CAF50',
      tags: ['maintenance', 'prediction', 'failure', 'analysis']
    });

    Debug.log('NodeRegistry', '工业制造节点注册完成 - 共8个节点 (3个MES + 3个设备管理 + 2个预测性维护)');
  }

  /**
   * 构建分类索引
   */
  private buildCategoryIndex(): void {
    this.categories.clear();

    for (const nodeInfo of this.nodes.values()) {
      if (!this.categories.has(nodeInfo.category)) {
        this.categories.set(nodeInfo.category, []);
      }
      this.categories.get(nodeInfo.category)!.push(nodeInfo);
    }

    // 对每个分类的节点进行排序
    for (const nodes of this.categories.values()) {
      nodes.sort((a, b) => a.name.localeCompare(b.name));
    }
  }

  /**
   * 获取节点信息
   */
  getNodeInfo(type: string): NodeInfo | undefined {
    return this.nodes.get(type);
  }

  /**
   * 获取所有节点
   */
  getAllNodes(): NodeInfo[] {
    return Array.from(this.nodes.values());
  }

  /**
   * 根据分类获取节点
   */
  getNodesByCategory(category: NodeCategory): NodeInfo[] {
    return this.categories.get(category) || [];
  }

  /**
   * 搜索节点
   */
  searchNodes(query: string): NodeInfo[] {
    const lowerQuery = query.toLowerCase();
    return Array.from(this.nodes.values()).filter(nodeInfo => 
      nodeInfo.name.toLowerCase().includes(lowerQuery) ||
      nodeInfo.description.toLowerCase().includes(lowerQuery) ||
      nodeInfo.tags?.some(tag => tag.toLowerCase().includes(lowerQuery))
    );
  }

  /**
   * 创建节点实例
   */
  createNode(type: string, id?: string): VisualScriptNode | null {
    const nodeInfo = this.nodes.get(type);
    if (!nodeInfo) {
      Debug.warn('NodeRegistry', `未找到节点类型: ${type}`);
      return null;
    }

    try {
      return new nodeInfo.nodeClass(type, nodeInfo.name, id);
    } catch (error) {
      Debug.error('NodeRegistry', `节点创建失败: ${type}`, error);
      return null;
    }
  }

  /**
   * 获取分类信息
   */
  getCategoryInfo(): { category: NodeCategory; name: string; count: number }[] {
    const categoryNames: { [key in NodeCategory]: string } = {
      [NodeCategory.MOTION_CAPTURE]: '动作捕捉',
      [NodeCategory.ENTITY_MANAGEMENT]: '实体管理',
      [NodeCategory.COMPONENT_MANAGEMENT]: '组件管理',
      [NodeCategory.TRANSFORM]: '变换操作',
      [NodeCategory.PHYSICS]: '物理系统',
      [NodeCategory.ANIMATION]: '动画系统',
      [NodeCategory.INPUT]: '输入系统',
      [NodeCategory.AUDIO]: '音频系统',
      [NodeCategory.SCENE_GENERATION]: '场景生成',
      [NodeCategory.WATER_SYSTEM]: '水系统',
      [NodeCategory.PARTICLE_SYSTEM]: '粒子系统',
      [NodeCategory.POST_PROCESS]: '后处理',
      [NodeCategory.TERRAIN_SYSTEM]: '地形系统',
      [NodeCategory.BLOCKCHAIN]: '区块链',
      [NodeCategory.LEARNING_RECORD]: '学习记录',
      [NodeCategory.UI_INTERFACE]: 'UI界面',
      [NodeCategory.RAG_APPLICATION]: 'RAG应用',
      [NodeCategory.SPATIAL_INFORMATION]: '空间信息',
      [NodeCategory.NETWORK]: '网络通信',
      [NodeCategory.RENDERING]: '渲染系统',
      [NodeCategory.ADVANCED_ANIMATION]: '高级动画',
      [NodeCategory.INDUSTRIAL_AUTOMATION]: '工业自动化',
      [NodeCategory.ADVANCED_AUDIO]: '高级音频',
      [NodeCategory.VR_INPUT]: 'VR输入',
      [NodeCategory.RENDERING_OPTIMIZATION]: '渲染优化',
      [NodeCategory.SOFT_BODY_PHYSICS]: '软体物理',
      [NodeCategory.COMPUTER_VISION]: '计算机视觉',
      [NodeCategory.SCENE_MANAGEMENT]: '场景管理',
      [NodeCategory.RESOURCE_MANAGEMENT]: '资源管理',
      [NodeCategory.RESOURCE_OPTIMIZATION]: '资源优化',
      [NodeCategory.ADVANCED_INPUT]: '高级输入',
      [NodeCategory.SENSOR_INPUT]: '传感器输入',
      [NodeCategory.VR_AR_INPUT]: 'VR/AR输入',
      [NodeCategory.PROJECT_MANAGEMENT]: '项目管理',
      [NodeCategory.AI_SERVICE]: 'AI服务',
      [NodeCategory.DATA_SERVICE]: '数据服务',
      [NodeCategory.AUTHENTICATION]: '认证授权',
      [NodeCategory.FILE_SERVICE]: '文件服务'
    };

    return Array.from(this.categories.entries()).map(([category, nodes]) => ({
      category,
      name: categoryNames[category],
      count: nodes.length
    }));
  }

  /**
   * 获取统计信息
   */
  getStatistics(): {
    totalNodes: number;
    categoriesCount: number;
    nodesByCategory: { [key: string]: number };
  } {
    const nodesByCategory: { [key: string]: number } = {};
    
    for (const [category, nodes] of this.categories.entries()) {
      nodesByCategory[category] = nodes.length;
    }

    return {
      totalNodes: this.nodes.size,
      categoriesCount: this.categories.size,
      nodesByCategory
    };
  }
}

// 导出单例实例
export const NodeRegistry = new NodeRegistryManager();
