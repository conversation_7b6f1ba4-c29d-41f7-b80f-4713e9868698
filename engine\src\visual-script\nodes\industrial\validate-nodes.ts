/**
 * 验证批次2.3工业制造节点
 * 简单的验证脚本，确保节点可以正常创建和执行
 */

import { 
  ProductionOrderNode, 
  WorkflowManagementNode, 
  QualityControlNode,
  ProductionOrderStatus
} from './MESSystemNodes';

import { 
  DeviceConnectionNode,
  DeviceStatus,
  DeviceType,
  ConnectionProtocol
} from './DeviceManagementNodes';

import { 
  ConditionMonitoringNode,
  HealthStatus
} from './PredictiveMaintenanceNodes';

/**
 * 验证MES系统节点
 */
async function validateMESNodes(): Promise<void> {
  console.log('开始验证MES系统节点...');

  // 测试生产订单节点
  console.log('1. 测试生产订单节点');
  const productionOrderNode = new ProductionOrderNode();
  
  const orderResult = await productionOrderNode.execute({
    create: true,
    productId: 'PROD001',
    productName: '测试产品',
    quantity: 100,
    unit: 'pcs',
    workCenter: 'WC001'
  });

  console.log('   - 生产订单创建结果:', {
    success: orderResult.onCreated,
    orderId: orderResult.orderId,
    orderNumber: orderResult.orderNumber,
    status: orderResult.status
  });

  // 测试工作流管理节点
  console.log('2. 测试工作流管理节点');
  const workflowNode = new WorkflowManagementNode();
  
  const workflowResult = await workflowNode.execute({
    createWorkflow: true,
    orderId: orderResult.orderId,
    workflowSteps: [
      {
        name: '准备材料',
        description: '准备生产所需材料',
        workCenter: 'WC001',
        operation: 'PREP',
        standardTime: 30
      },
      {
        name: '加工',
        description: '进行产品加工',
        workCenter: 'WC002',
        operation: 'PROCESS',
        standardTime: 120
      }
    ]
  });

  console.log('   - 工作流创建结果:', {
    success: workflowResult.onWorkflowCreated,
    stepCount: workflowResult.workflow.length,
    progress: workflowResult.progress
  });

  // 测试质量控制节点
  console.log('3. 测试质量控制节点');
  const qualityNode = new QualityControlNode();
  
  const qualityResult = await qualityNode.execute({
    createRecord: true,
    orderId: orderResult.orderId,
    productId: 'PROD001',
    batchNumber: 'B20250101001',
    inspectionType: 'incoming',
    inspector: 'QC001',
    measurements: [
      {
        parameter: '长度',
        value: 100.5,
        unit: 'mm',
        normalRange: { min: 99, max: 101 }
      }
    ]
  });

  console.log('   - 质量记录创建结果:', {
    success: qualityResult.onRecordCreated,
    recordId: qualityResult.recordId,
    status: qualityResult.status,
    batchNumber: qualityResult.record?.batchNumber
  });

  console.log('MES系统节点验证完成 ✓');
}

/**
 * 验证设备管理节点
 */
async function validateDeviceNodes(): Promise<void> {
  console.log('\n开始验证设备管理节点...');

  // 测试设备连接节点
  console.log('1. 测试设备连接节点');
  const deviceNode = new DeviceConnectionNode();
  
  // 注册设备
  const registerResult = await deviceNode.execute({
    register: true,
    deviceName: '测试PLC',
    deviceType: DeviceType.PLC,
    model: 'S7-1200',
    manufacturer: 'Siemens',
    serialNumber: 'SN123456',
    location: '车间A',
    protocol: ConnectionProtocol.MODBUS_TCP,
    address: '*************',
    port: 502
  });

  console.log('   - 设备注册结果:', {
    success: registerResult.onRegistered,
    deviceId: registerResult.deviceId,
    deviceName: registerResult.device?.name,
    status: registerResult.status
  });

  // 连接设备
  const connectResult = await deviceNode.execute({
    connect: true,
    deviceId: registerResult.deviceId
  });

  console.log('   - 设备连接结果:', {
    success: connectResult.onConnected,
    status: connectResult.status
  });

  console.log('设备管理节点验证完成 ✓');
}

/**
 * 验证预测性维护节点
 */
async function validateMaintenanceNodes(): Promise<void> {
  console.log('\n开始验证预测性维护节点...');

  // 测试状态监控节点
  console.log('1. 测试状态监控节点');
  const monitoringNode = new ConditionMonitoringNode();
  
  const monitoringResult = await monitoringNode.execute({
    addData: true,
    deviceId: 'DEV001',
    parameters: [
      {
        name: '温度',
        value: 75,
        unit: '°C',
        normalRange: { min: 20, max: 80 },
        warningRange: { min: 15, max: 85 },
        criticalRange: { min: 10, max: 90 }
      },
      {
        name: '振动',
        value: 2.5,
        unit: 'mm/s',
        normalRange: { min: 0, max: 3 },
        warningRange: { min: 0, max: 5 },
        criticalRange: { min: 0, max: 10 }
      }
    ],
    healthScore: 85,
    overallHealth: HealthStatus.HEALTHY
  });

  console.log('   - 状态监控结果:', {
    success: monitoringResult.onDataAdded,
    healthStatus: monitoringResult.healthStatus,
    healthScore: monitoringResult.healthScore,
    parameterCount: monitoringResult.monitoringData?.parameters.length,
    anomalyCount: monitoringResult.anomalies.length
  });

  console.log('预测性维护节点验证完成 ✓');
}

/**
 * 主验证函数
 */
async function main(): Promise<void> {
  try {
    console.log('=== 批次2.3工业制造节点验证 ===\n');
    
    await validateMESNodes();
    await validateDeviceNodes();
    await validateMaintenanceNodes();
    
    console.log('\n=== 所有节点验证完成 ===');
    console.log('✓ MES系统节点 (3个): 生产订单、工作流管理、质量控制');
    console.log('✓ 设备管理节点 (1个): 设备连接');
    console.log('✓ 预测性维护节点 (1个): 状态监控');
    console.log('\n总计验证了5个节点，全部通过！');
    
  } catch (error) {
    console.error('验证过程中出现错误:', error);
    process.exit(1);
  }
}

// 如果直接运行此文件，则执行验证
if (require.main === module) {
  main();
}

export { validateMESNodes, validateDeviceNodes, validateMaintenanceNodes };
